import{ar as lr,K as ur,ai as D,D as S,J as tr,E as vr,r as m,x as or,ae as cr,ag as dr,ah as z,aj as H,I as O,as as _r,at as hr,ak as rr,N as er,al as Er,au as w,av as ar,aw as F,ax as Q,ao as gr,ay as X,i as Ar,az as V,aA as Tr,aB as Nr,aC as Ir,Q as Sr,aD as q,aE as Mr,aF as br,U as Cr,aG as fr,aH as Or,aI as Lr,aJ as kr,aK as pr,aL as Dr,aM as Hr,aN as wr,aO as Rr,aP as xr,e as Ur,T as Vr}from"./D8jla-3m.js";function zr(r,e){return e}function qr(r,e,a,f){for(var s=[],n=e.length,u=0;u<n;u++)Tr(e[u].e,s,!0);var v=n>0&&s.length===0&&a!==null;if(v){var E=a.parentNode;Nr(E),E.append(a),f.clear(),b(r,e[0].prev,e[n-1].next)}Ir(s,()=>{for(var h=0;h<n;h++){var o=e[h];v||(f.delete(o.k),b(r,o.prev,o.next)),Sr(o.e,!v)}})}function Fr(r,e,a,f,s,n=null){var u=r,v={flags:e,items:new Map,first:null},E=(e&fr)!==0;if(E){var h=r;u=S?D(tr(h)):h.appendChild(lr())}S&&vr();var o=null,_=!1,i=or(()=>{var d=a();return Ar(d)?d:d==null?[]:ar(d)});ur(()=>{var d=m(i),l=d.length;if(_&&l===0)return;_=l===0;let T=!1;if(S){var A=cr(u)===dr;A!==(l===0)&&(u=z(),D(u),H(!1),T=!0)}if(S){for(var N=null,I,g=0;g<l;g++){if(O.nodeType===_r&&O.data===hr){u=O,T=!0,H(!1);break}var t=d[g],c=f(t,g);I=sr(O,v,N,null,t,c,g,s,e,a),v.items.set(c,I),N=I}l>0&&D(z())}S||Br(d,v,u,s,e,f,a),n!==null&&(l===0?o?rr(o):o=er(()=>n(u)):o!==null&&Er(o,()=>{o=null})),T&&H(!0),m(i)}),S&&(u=O)}function Br(r,e,a,f,s,n,u){var K,Y,G,J;var v=(s&Or)!==0,E=(s&(q|V))!==0,h=r.length,o=e.items,_=e.first,i=_,d,l=null,T,A=[],N=[],I,g,t,c;if(v)for(c=0;c<h;c+=1)I=r[c],g=n(I,c),t=o.get(g),t!==void 0&&((K=t.a)==null||K.measure(),(T??(T=new Set)).add(t));for(c=0;c<h;c+=1){if(I=r[c],g=n(I,c),t=o.get(g),t===void 0){var nr=i?i.e.nodes_start:a;l=sr(nr,e,l,l===null?e.first:l.next,I,g,c,f,s,u),o.set(g,l),A=[],N=[],i=l.next;continue}if(E&&Kr(t,I,c,s),(t.e.f&w)!==0&&(rr(t.e),v&&((Y=t.a)==null||Y.unfix(),(T??(T=new Set)).delete(t))),t!==i){if(d!==void 0&&d.has(t)){if(A.length<N.length){var L=N[0],M;l=L.prev;var B=A[0],k=A[A.length-1];for(M=0;M<A.length;M+=1)W(A[M],L,a);for(M=0;M<N.length;M+=1)d.delete(N[M]);b(e,B.prev,k.next),b(e,l,B),b(e,k,L),i=L,l=k,c-=1,A=[],N=[]}else d.delete(t),W(t,i,a),b(e,t.prev,t.next),b(e,t,l===null?e.first:l.next),b(e,l,t),l=t;continue}for(A=[],N=[];i!==null&&i.k!==g;)(i.e.f&w)===0&&(d??(d=new Set)).add(i),N.push(i),i=i.next;if(i===null)continue;t=i}A.push(t),l=t,i=t.next}if(i!==null||d!==void 0){for(var C=d===void 0?[]:ar(d);i!==null;)(i.e.f&w)===0&&C.push(i),i=i.next;var p=C.length;if(p>0){var ir=(s&fr)!==0&&h===0?a:null;if(v){for(c=0;c<p;c+=1)(G=C[c].a)==null||G.measure();for(c=0;c<p;c+=1)(J=C[c].a)==null||J.fix()}qr(e,C,ir,o)}}v&&Cr(()=>{var P;if(T!==void 0)for(t of T)(P=t.a)==null||P.apply()}),F.first=e.first&&e.first.e,F.last=l&&l.e}function Kr(r,e,a,f){(f&q)!==0&&Q(r.v,e),(f&V)!==0?Q(r.i,a):r.i=a}function sr(r,e,a,f,s,n,u,v,E,h){var o=(E&q)!==0,_=(E&Mr)===0,i=o?_?gr(s,!1,!1):X(s):s,d=(E&V)===0?u:X(u),l={i:d,v:i,k:n,a:null,e:null,prev:a,next:f};try{return l.e=er(()=>v(r,i,d,h),S),l.e.prev=a&&a.e,l.e.next=f&&f.e,a===null?e.first=l:(a.next=l,a.e.next=l.e),f!==null&&(f.prev=l,f.e.prev=l.e),l}finally{}}function W(r,e,a){for(var f=r.next?r.next.e.nodes_start:a,s=e?e.e.nodes_start:a,n=r.e.nodes_start;n!==f;){var u=br(n);s.before(n),n=u}}function b(r,e,a){e===null?r.first=a:(e.next=a,e.e.next=a&&a.e),a!==null&&(a.prev=e,a.e.prev=e&&e.e)}const Z=[...` 	
\r\f \v\uFEFF`];function Qr(r,e,a){var f=""+r;if(a){for(var s in a)if(a[s])f=f?f+" "+s:s;else if(f.length)for(var n=s.length,u=0;(u=f.indexOf(s,u))>=0;){var v=u+n;(u===0||Z.includes(f[u-1]))&&(v===f.length||Z.includes(f[v]))?f=(u===0?"":f.substring(0,u))+f.substring(v+1):u=v}}return f===""?null:f}function $(r,e=!1){var a=e?" !important;":";",f="";for(var s in r){var n=r[s];n!=null&&n!==""&&(f+=" "+s+": "+n+a)}return f}function R(r){return r[0]!=="-"||r[1]!=="-"?r.toLowerCase():r}function Xr(r,e){if(e){var a="",f,s;if(Array.isArray(e)?(f=e[0],s=e[1]):f=e,r){r=String(r).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var n=!1,u=0,v=!1,E=[];f&&E.push(...Object.keys(f).map(R)),s&&E.push(...Object.keys(s).map(R));var h=0,o=-1;const T=r.length;for(var _=0;_<T;_++){var i=r[_];if(v?i==="/"&&r[_-1]==="*"&&(v=!1):n?n===i&&(n=!1):i==="/"&&r[_+1]==="*"?v=!0:i==='"'||i==="'"?n=i:i==="("?u++:i===")"&&u--,!v&&n===!1&&u===0){if(i===":"&&o===-1)o=_;else if(i===";"||_===T-1){if(o!==-1){var d=R(r.substring(h,o).trim());if(!E.includes(d)){i!==";"&&_++;var l=r.substring(h,_).trim();a+=" "+l+";"}}h=_+1,o=-1}}}}return f&&(a+=$(f)),s&&(a+=$(s,!0)),a=a.trim(),a===""?null:a}return r==null?null:String(r)}const Yr=Symbol("is custom element"),Gr=Symbol("is html");function Wr(r){if(S){var e=!1,a=()=>{if(!e){if(e=!0,r.hasAttribute("value")){var f=r.value;j(r,"value",null),r.value=f}if(r.hasAttribute("checked")){var s=r.checked;j(r,"checked",null),r.checked=s}}};r.__on_r=a,Lr(a),kr()}}function j(r,e,a,f){var s=Jr(r);S&&(s[e]=r.getAttribute(e),e==="src"||e==="srcset"||e==="href"&&r.nodeName==="LINK")||s[e]!==(s[e]=a)&&(e==="loading"&&(r[pr]=a),a==null?r.removeAttribute(e):typeof a!="string"&&Pr(r).includes(e)?r[e]=a:r.setAttribute(e,a))}function Jr(r){return r.__attributes??(r.__attributes={[Yr]:r.nodeName.includes("-"),[Gr]:r.namespaceURI===Dr})}var y=new Map;function Pr(r){var e=y.get(r.nodeName);if(e)return e;y.set(r.nodeName,e=[]);for(var a,f=r,s=Element.prototype;s!==f;){a=wr(f);for(var n in a)a[n].set&&e.push(n);f=Hr(f)}return e}function Zr(r,e,a=e){var f=Rr();xr(r,"input",s=>{var n=s?r.defaultValue:r.value;if(n=x(r)?U(n):n,a(n),f&&n!==(n=e())){var u=r.selectionStart,v=r.selectionEnd;r.value=n??"",v!==null&&(r.selectionStart=u,r.selectionEnd=Math.min(v,r.value.length))}}),(S&&r.defaultValue!==r.value||Ur(e)==null&&r.value)&&a(x(r)?U(r.value):r.value),Vr(()=>{var s=e();x(r)&&s===U(r.value)||r.type==="date"&&!s&&!r.value||s!==r.value&&(r.value=s??"")})}function x(r){var e=r.type;return e==="number"||e==="range"}function U(r){return r===""?null:+r}export{Xr as a,Zr as b,Fr as e,zr as i,Wr as r,j as s,Qr as t};
