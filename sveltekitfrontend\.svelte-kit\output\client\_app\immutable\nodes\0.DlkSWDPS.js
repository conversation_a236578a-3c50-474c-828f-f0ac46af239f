var Dt=e=>{throw TypeError(e)};var bt=(e,t,s)=>t.has(e)||Dt("Cannot "+s);var r=(e,t,s)=>(bt(e,t,"read from private field"),s?s.call(e):t.get(e)),_=(e,t,s)=>t.has(e)?Dt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),g=(e,t,s,a)=>(bt(e,t,"write to private field"),a?a.call(e,s):t.set(e,s),s),L=(e,t,s)=>(bt(e,t,"access private method"),s);var vt=(e,t,s,a)=>({set _(i){g(e,t,i,s)},get _(){return r(e,t,a)}});import"../chunks/CWj6FrbW.js";import{i as Nt}from"../chunks/COxqGmGD.js";import{D as Pt,E as Xt,V as ft,ad as Gt,ab as zt,X as q,Y as pt,ac as Qt,W as H,_ as Q,w as z,Z as tt,a3 as A,r as b,$ as P,a0 as lt,a1 as ct,a2 as Zt,aS as Jt,v as jt,aa as te,a9 as ee}from"../chunks/D8jla-3m.js";import{i as ht,s as Ct,a as gt}from"../chunks/BFoZa56j.js";import{t as se,e as Lt,r as wt,b as _t}from"../chunks/BPG35sX8.js";import{p as ae}from"../chunks/CVBcWiqP.js";import{g as re}from"../chunks/XmytlI-B.js";import{S as Ut,h as Bt,Q as ie,n as k,m as At,R as ne,e as oe,f as kt,g as N,i as ue,j as le,k as ce,l as he,o as xt,r as $t,p as de,q as Ft,v as St,w as fe,x as pe,s as qt,y as ve,z as Ot}from"../chunks/CBqDoOLn.js";import{o as me,a as ye}from"../chunks/50nLBstc.js";import{p as ge}from"../chunks/9302ZZM5.js";function Wt(e,t,s,a,i){var l;Pt&&Xt();var n=(l=t.$$slots)==null?void 0:l[s],o=!1;n===!0&&(n=t.children,o=!0),n===void 0||n(e,o?()=>a:a)}function mt(e,t,s,a,i,n){var o=e.__className;if(Pt||o!==s||o===void 0){var l=se(s,a,n);(!Pt||l!==e.getAttribute("class"))&&(l==null?e.removeAttribute("class"):e.className=l),e.__className=s}else if(n&&i!==n)for(var c in n){var f=!!n[c];(i==null||f!==!!i[c])&&e.classList.toggle(c,f)}return n}var R,Rt,be=(Rt=class extends Ut{constructor(t={}){super();_(this,R);this.config=t,g(this,R,new Map)}build(t,s,a){const i=s.queryKey,n=s.queryHash??Bt(i,s);let o=this.get(n);return o||(o=new ie({client:t,queryKey:i,queryHash:n,options:t.defaultQueryOptions(s),state:a,defaultOptions:t.getQueryDefaults(i)}),this.add(o)),o}add(t){r(this,R).has(t.queryHash)||(r(this,R).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const s=r(this,R).get(t.queryHash);s&&(t.destroy(),s===t&&r(this,R).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){k.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return r(this,R).get(t)}getAll(){return[...r(this,R).values()]}find(t){const s={exact:!0,...t};return this.getAll().find(a=>At(s,a))}findAll(t={}){const s=this.getAll();return Object.keys(t).length>0?s.filter(a=>At(t,a)):s}notify(t){k.batch(()=>{this.listeners.forEach(s=>{s(t)})})}onFocus(){k.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){k.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},R=new WeakMap,Rt),I,j,J,K,U,It,Pe=(It=class extends ne{constructor(t){super();_(this,K);_(this,I);_(this,j);_(this,J);this.mutationId=t.mutationId,g(this,j,t.mutationCache),g(this,I,[]),this.state=t.state||we(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){r(this,I).includes(t)||(r(this,I).push(t),this.clearGcTimeout(),r(this,j).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){g(this,I,r(this,I).filter(s=>s!==t)),this.scheduleGc(),r(this,j).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){r(this,I).length||(this.state.status==="pending"?this.scheduleGc():r(this,j).remove(this))}continue(){var t;return((t=r(this,J))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var n,o,l,c,f,C,h,p,w,M,u,v,D,x,m,O,$,V,X,Z;const s=()=>{L(this,K,U).call(this,{type:"continue"})};g(this,J,oe({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(y,T)=>{L(this,K,U).call(this,{type:"failed",failureCount:y,error:T})},onPause:()=>{L(this,K,U).call(this,{type:"pause"})},onContinue:s,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>r(this,j).canRun(this)}));const a=this.state.status==="pending",i=!r(this,J).canStart();try{if(a)s();else{L(this,K,U).call(this,{type:"pending",variables:t,isPaused:i}),await((o=(n=r(this,j).config).onMutate)==null?void 0:o.call(n,t,this));const T=await((c=(l=this.options).onMutate)==null?void 0:c.call(l,t));T!==this.state.context&&L(this,K,U).call(this,{type:"pending",context:T,variables:t,isPaused:i})}const y=await r(this,J).start();return await((C=(f=r(this,j).config).onSuccess)==null?void 0:C.call(f,y,t,this.state.context,this)),await((p=(h=this.options).onSuccess)==null?void 0:p.call(h,y,t,this.state.context)),await((M=(w=r(this,j).config).onSettled)==null?void 0:M.call(w,y,null,this.state.variables,this.state.context,this)),await((v=(u=this.options).onSettled)==null?void 0:v.call(u,y,null,t,this.state.context)),L(this,K,U).call(this,{type:"success",data:y}),y}catch(y){try{throw await((x=(D=r(this,j).config).onError)==null?void 0:x.call(D,y,t,this.state.context,this)),await((O=(m=this.options).onError)==null?void 0:O.call(m,y,t,this.state.context)),await((V=($=r(this,j).config).onSettled)==null?void 0:V.call($,void 0,y,this.state.variables,this.state.context,this)),await((Z=(X=this.options).onSettled)==null?void 0:Z.call(X,void 0,y,t,this.state.context)),y}finally{L(this,K,U).call(this,{type:"error",error:y})}}finally{r(this,j).runNext(this)}}},I=new WeakMap,j=new WeakMap,J=new WeakMap,K=new WeakSet,U=function(t){const s=a=>{switch(t.type){case"failed":return{...a,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...a,isPaused:!0};case"continue":return{...a,isPaused:!1};case"pending":return{...a,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...a,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...a,data:void 0,error:t.error,failureCount:a.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=s(this.state),k.batch(()=>{r(this,I).forEach(a=>{a.onMutationUpdate(t)}),r(this,j).notify({mutation:this,type:"updated",action:t})})},It);function we(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var G,E,dt,Kt,_e=(Kt=class extends Ut{constructor(t={}){super();_(this,G);_(this,E);_(this,dt);this.config=t,g(this,G,new Set),g(this,E,new Map),g(this,dt,0)}build(t,s,a){const i=new Pe({mutationCache:this,mutationId:++vt(this,dt)._,options:t.defaultMutationOptions(s),state:a});return this.add(i),i}add(t){r(this,G).add(t);const s=yt(t);if(typeof s=="string"){const a=r(this,E).get(s);a?a.push(t):r(this,E).set(s,[t])}this.notify({type:"added",mutation:t})}remove(t){if(r(this,G).delete(t)){const s=yt(t);if(typeof s=="string"){const a=r(this,E).get(s);if(a)if(a.length>1){const i=a.indexOf(t);i!==-1&&a.splice(i,1)}else a[0]===t&&r(this,E).delete(s)}}this.notify({type:"removed",mutation:t})}canRun(t){const s=yt(t);if(typeof s=="string"){const a=r(this,E).get(s),i=a==null?void 0:a.find(n=>n.state.status==="pending");return!i||i===t}else return!0}runNext(t){var a;const s=yt(t);if(typeof s=="string"){const i=(a=r(this,E).get(s))==null?void 0:a.find(n=>n!==t&&n.state.isPaused);return(i==null?void 0:i.continue())??Promise.resolve()}else return Promise.resolve()}clear(){k.batch(()=>{r(this,G).forEach(t=>{this.notify({type:"removed",mutation:t})}),r(this,G).clear(),r(this,E).clear()})}getAll(){return Array.from(r(this,G))}find(t){const s={exact:!0,...t};return this.getAll().find(a=>kt(s,a))}findAll(t={}){return this.getAll().filter(s=>kt(t,s))}notify(t){k.batch(()=>{this.listeners.forEach(s=>{s(t)})})}resumePausedMutations(){const t=this.getAll().filter(s=>s.state.isPaused);return k.batch(()=>Promise.all(t.map(s=>s.continue().catch(N))))}},G=new WeakMap,E=new WeakMap,dt=new WeakMap,Kt);function yt(e){var t;return(t=e.options.scope)==null?void 0:t.id}function Et(e){return{onFetch:(t,s)=>{var C,h,p,w,M;const a=t.options,i=(p=(h=(C=t.fetchOptions)==null?void 0:C.meta)==null?void 0:h.fetchMore)==null?void 0:p.direction,n=((w=t.state.data)==null?void 0:w.pages)||[],o=((M=t.state.data)==null?void 0:M.pageParams)||[];let l={pages:[],pageParams:[]},c=0;const f=async()=>{let u=!1;const v=m=>{Object.defineProperty(m,"signal",{enumerable:!0,get:()=>(t.signal.aborted?u=!0:t.signal.addEventListener("abort",()=>{u=!0}),t.signal)})},D=ue(t.options,t.fetchOptions),x=async(m,O,$)=>{if(u)return Promise.reject();if(O==null&&m.pages.length)return Promise.resolve(m);const X=(()=>{const it={client:t.client,queryKey:t.queryKey,pageParam:O,direction:$?"backward":"forward",meta:t.options.meta};return v(it),it})(),Z=await D(X),{maxPages:y}=t.options,T=$?le:ce;return{pages:T(m.pages,Z,y),pageParams:T(m.pageParams,O,y)}};if(i&&n.length){const m=i==="backward",O=m?Oe:Tt,$={pages:n,pageParams:o},V=O(a,$);l=await x($,V,m)}else{const m=e??n.length;do{const O=c===0?o[0]??a.initialPageParam:Tt(a,l);if(c>0&&O==null)break;l=await x(l,O),c++}while(c<m)}return l};t.options.persister?t.fetchFn=()=>{var u,v;return(v=(u=t.options).persister)==null?void 0:v.call(u,f,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s)}:t.fetchFn=f}}}function Tt(e,{pages:t,pageParams:s}){const a=t.length-1;return t.length>0?e.getNextPageParam(t[a],t,s[a],s):void 0}function Oe(e,{pages:t,pageParams:s}){var a;return t.length>0?(a=e.getPreviousPageParam)==null?void 0:a.call(e,t[0],t,s[0],s):void 0}var d,B,W,et,st,Y,at,rt,Ht,Yt=(Ht=class{constructor(e={}){_(this,d);_(this,B);_(this,W);_(this,et);_(this,st);_(this,Y);_(this,at);_(this,rt);g(this,d,e.queryCache||new be),g(this,B,e.mutationCache||new _e),g(this,W,e.defaultOptions||{}),g(this,et,new Map),g(this,st,new Map),g(this,Y,0)}mount(){vt(this,Y)._++,r(this,Y)===1&&(g(this,at,he.subscribe(async e=>{e&&(await this.resumePausedMutations(),r(this,d).onFocus())})),g(this,rt,xt.subscribe(async e=>{e&&(await this.resumePausedMutations(),r(this,d).onOnline())})))}unmount(){var e,t;vt(this,Y)._--,r(this,Y)===0&&((e=r(this,at))==null||e.call(this),g(this,at,void 0),(t=r(this,rt))==null||t.call(this),g(this,rt,void 0))}isFetching(e){return r(this,d).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return r(this,B).findAll({...e,status:"pending"}).length}getQueryData(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=r(this,d).get(t.queryHash))==null?void 0:s.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=r(this,d).build(this,t),a=s.state.data;return a===void 0?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime($t(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(a))}getQueriesData(e){return r(this,d).findAll(e).map(({queryKey:t,state:s})=>{const a=s.data;return[t,a]})}setQueryData(e,t,s){const a=this.defaultQueryOptions({queryKey:e}),i=r(this,d).get(a.queryHash),n=i==null?void 0:i.state.data,o=de(t,n);if(o!==void 0)return r(this,d).build(this,a).setData(o,{...s,manual:!0})}setQueriesData(e,t,s){return k.batch(()=>r(this,d).findAll(e).map(({queryKey:a})=>[a,this.setQueryData(a,t,s)]))}getQueryState(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=r(this,d).get(t.queryHash))==null?void 0:s.state}removeQueries(e){const t=r(this,d);k.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=r(this,d);return k.batch(()=>(s.findAll(e).forEach(a=>{a.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const s={revert:!0,...t},a=k.batch(()=>r(this,d).findAll(e).map(i=>i.cancel(s)));return Promise.all(a).then(N).catch(N)}invalidateQueries(e,t={}){return k.batch(()=>(r(this,d).findAll(e).forEach(s=>{s.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},a=k.batch(()=>r(this,d).findAll(e).filter(i=>!i.isDisabled()&&!i.isStatic()).map(i=>{let n=i.fetch(void 0,s);return s.throwOnError||(n=n.catch(N)),i.state.fetchStatus==="paused"?Promise.resolve():n}));return Promise.all(a).then(N)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=r(this,d).build(this,t);return s.isStaleByTime($t(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(N).catch(N)}fetchInfiniteQuery(e){return e.behavior=Et(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(N).catch(N)}ensureInfiniteQueryData(e){return e.behavior=Et(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return xt.isOnline()?r(this,B).resumePausedMutations():Promise.resolve()}getQueryCache(){return r(this,d)}getMutationCache(){return r(this,B)}getDefaultOptions(){return r(this,W)}setDefaultOptions(e){g(this,W,e)}setQueryDefaults(e,t){r(this,et).set(Ft(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...r(this,et).values()],s={};return t.forEach(a=>{St(e,a.queryKey)&&Object.assign(s,a.defaultOptions)}),s}setMutationDefaults(e,t){r(this,st).set(Ft(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...r(this,st).values()],s={};return t.forEach(a=>{St(e,a.mutationKey)&&Object.assign(s,a.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...r(this,W).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Bt(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===fe&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...r(this,W).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){r(this,d).clear(),r(this,B).clear()}},d=new WeakMap,B=new WeakMap,W=new WeakMap,et=new WeakMap,st=new WeakMap,Y=new WeakMap,at=new WeakMap,rt=new WeakMap,Ht);function Qe(e,t){ft(t,!1);let s=ge(t,"client",24,()=>new Yt);me(()=>{s().mount()}),pe(s()),ye(()=>{s().unmount()}),Nt();var a=Gt(),i=zt(a);Wt(i,t,"default",{}),q(e,a),pt()}var Ce=(e,t)=>z(t,!b(t)),Me=(e,t,s)=>t(s()),De=H('<button class="menu-item svelte-e66z8m"><span class="check svelte-e66z8m"> </span> </button>'),je=H('<div class="menu svelte-e66z8m" role="menu"></div>'),Ae=H('<div class="settings-container svelte-e66z8m"><button title="Settings">⚙️</button> <!></div>');function ke(e,t){ft(t,!0);const[s,a]=Ct(),i=()=>gt(qt,"$settings",s);let n=tt(!1);function o(h){qt.update(p=>(p[h].value=!p[h].value,p))}var l=Ae(),c=Q(l);c.__click=[Ce,n];var f=A(c,2);{var C=h=>{var p=je();Lt(p,5,()=>Object.entries(i()),([w,M])=>w,(w,M)=>{var u=Zt(()=>Jt(b(M),2));let v=()=>b(u)[0],D=()=>b(u)[1];var x=De();x.__click=[Me,o,v];var m=Q(x),O=Q(m,!0);P(m);var $=A(m);P(x),lt(()=>{ct(O,D().value?"✅":"❌"),ct($,` ${D().displayName??""}`)}),q(w,x)}),P(p),q(h,p)};ht(f,h=>{b(n)&&h(C)})}P(l),q(e,l),pt(),a()}Qt(["click"]);var xe=H("<option> </option>"),$e=H('<datalist id="channels-list"></datalist>'),Fe=H('<div class="filters-wrapper svelte-1ootrjn"><form class="svelte-1ootrjn"><div class="input-group svelte-1ootrjn"><input list="channels-list" name="channel" placeholder="Channel or ID"/> <!> <input name="username" placeholder="Username or ID"/> <button type="submit" class="svelte-1ootrjn">Load</button></div> <div class="nav-group svelte-1ootrjn"><a href="/search">Global Search</a> <a href="/analytics">Analytics</a> <a href="/tracker">User Tracker</a></div> <div class="actions-group svelte-1ootrjn"><!> <a href="/redoc" target="_blank" title="API Docs" class="svelte-1ootrjn">API</a> <button type="button" title="Opt-out">Opt-out</button></div></form></div>');function Se(e,t){ft(t,!0);const[s,a]=Ct(),i=()=>gt(ae,"$page",s),n=()=>gt(Ot,"$showOptout",s);let o=tt(jt(i().url.searchParams.get("channel")||"")),l=tt(jt(i().url.searchParams.get("username")||""));const c=ve();function f(F){F.preventDefault();const S=new URL(i().url);b(o)?S.searchParams.set("channel",b(o).toLowerCase().trim()):S.searchParams.delete("channel"),b(l)?S.searchParams.set("username",b(l).toLowerCase().trim()):S.searchParams.delete("username"),S.pathname="/",re(S,{invalidateAll:!0})}var C=Fe(),h=Q(C),p=Q(h),w=Q(p);wt(w);var M=A(w,2);{var u=F=>{var S=$e();Lt(S,21,()=>c.data,nt=>nt.userID,(nt,ot)=>{var ut=xe(),Vt=Q(ut,!0);P(ut);var Mt={};lt(()=>{ct(Vt,b(ot).name),Mt!==(Mt=b(ot).name)&&(ut.value=(ut.__value=b(ot).name)??"")}),q(nt,ut)}),P(S),q(F,S)};ht(M,F=>{c.data&&F(u)})}var v=A(M,2);wt(v),te(2),P(p);var D=A(p,2),x=Q(D);let m;var O=A(x,2);let $;var V=A(O,2);let X;P(D);var Z=A(D,2),y=Q(Z);ke(y,{});var T=A(y,4);T.__click=()=>Ot.update(F=>!F);let it;P(Z),P(h),P(C),lt((F,S,nt,ot)=>{m=mt(x,1,"svelte-1ootrjn",null,m,F),$=mt(O,1,"svelte-1ootrjn",null,$,S),X=mt(V,1,"svelte-1ootrjn",null,X,nt),it=mt(T,1,"svelte-1ootrjn",null,it,ot)},[()=>({active:i().url.pathname.startsWith("/search")}),()=>({active:i().url.pathname.startsWith("/analytics")}),()=>({active:i().url.pathname.startsWith("/tracker")}),()=>({active:n()})]),ee("submit",h,f),_t(w,()=>b(o),F=>z(o,F)),_t(v,()=>b(l),F=>z(l,F)),q(e,C),pt(),a()}Qt(["click"]);async function qe(e,t,s,a){z(t,!0),z(s,"");try{const i=await fetch("/optout",{method:"POST"});if(!i.ok)throw new Error(await i.text());const n=await i.json();z(a,n,!0)}catch(i){z(s,i.message,!0)}finally{z(t,!1)}}var Ee=H('<p class="small svelte-1j0ex2j">This code is valid for 60 seconds.</p>'),Te=H('<p class="error svelte-1j0ex2j"> </p>'),Re=H(`<div class="panel svelte-1j0ex2j"><h2>Opt-out of Logging</h2> <p>You can opt out from being logged. This will also delete all of your existing logs. This
		applies to all chats on this rustlog instance.</p> <p><strong>This action is permanent and cannot be reversed.</strong></p> <p>To opt out, generate a code below and paste the command into any logged Twitch chat.</p> <code class="svelte-1j0ex2j"></code> <div class="generator svelte-1j0ex2j"><input readonly="" type="text" placeholder="Your code will appear here" class="svelte-1j0ex2j"/> <button> </button></div> <!> <!></div>`);function Ie(e,t){ft(t,!0);let s=tt(""),a=tt(""),i=tt(!1);var n=Re(),o=A(Q(n),8);o.textContent="!rustlog optout <your-code>";var l=A(o,2),c=Q(l);wt(c);var f=A(c,2);f.__click=[qe,i,a,s];var C=Q(f,!0);P(f),P(l);var h=A(l,2);{var p=u=>{var v=Ee();q(u,v)};ht(h,u=>{b(s)&&u(p)})}var w=A(h,2);{var M=u=>{var v=Te(),D=Q(v,!0);P(v),lt(()=>ct(D,b(a))),q(u,v)};ht(w,u=>{b(a)&&u(M)})}P(n),lt(()=>{f.disabled=b(i),ct(C,b(i)?"Generating...":"Generate Code")}),_t(c,()=>b(s),u=>z(s,u)),q(e,n),pt()}Qt(["click"]);var Ke=H('<div class="layout svelte-dyn5zy"><header><!></header> <main class="svelte-dyn5zy"><!></main></div>');function Ze(e,t){ft(t,!1);const[s,a]=Ct(),i=()=>gt(Ot,"$showOptout",s),n=new Yt;Nt(),Qe(e,{get client(){return n},children:(o,l)=>{var c=Ke(),f=Q(c),C=Q(f);Se(C,{}),P(f);var h=A(f,2),p=Q(h);{var w=u=>{Ie(u,{})},M=u=>{var v=Gt(),D=zt(v);Wt(D,t,"default",{}),q(u,v)};ht(p,u=>{i()?u(w):u(M,!1)})}P(h),P(c),q(o,c)},$$slots:{default:!0}}),pt(),a()}export{Ze as component};
