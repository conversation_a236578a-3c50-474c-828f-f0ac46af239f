import"../chunks/CWj6FrbW.js";import{V as le,u as ne,r as e,W as i,X as r,Y as ve,w as k,Z as A,_ as l,$ as n,a0 as E,a1 as B,a2 as y,a3 as D}from"../chunks/D8jla-3m.js";import{i as h,s as pe,a as G}from"../chunks/BFoZa56j.js";import{r as me,b as ce}from"../chunks/BPG35sX8.js";import{L as ge,s as de}from"../chunks/CYYS3DFc.js";import{p as ue}from"../chunks/CVBcWiqP.js";import{u as fe,a as he,s as _e}from"../chunks/CBqDoOLn.js";import{V as Le}from"../chunks/2vYeMJ5I.js";var be=i('<div class="welcome-message svelte-f8gpsp"><h2>Welcome to rustlog</h2> <p>Please select a channel and username above to start viewing logs. <br/> Or use one of the global tools like Global Search or Analytics.</p></div>'),ye=i("<p>Loading available logs...</p>"),we=i('<p class="error svelte-f8gpsp"> </p>'),xe=i("<p>No logs found for this user in this channel.</p>"),Pe=i("<p>Loading logs...</p>"),Se=i("<div><!></div>"),Te=i('<div class="log-container svelte-f8gpsp"><div class="log-controls svelte-f8gpsp"><h3> </h3> <input type="search" placeholder="Search loaded logs..."/></div> <div class="list-container svelte-f8gpsp"><!></div></div>'),Ce=i("<div><!></div>");function We(X,Y){le(Y,!0);const[M,Z]=pe(),N=()=>G(ue,"$page",M),j=()=>G(_e,"$settings",M);let g=A(null),d=A(null),_=A("");ne(()=>{k(g,N().url.searchParams.get("channel"),!0),k(d,N().url.searchParams.get("username"),!0)});const L=fe(e(g),e(d)),w=y(()=>L.data??[]),x=y(()=>e(w).map(s=>he(e(g),e(d),s.year,s.month))),P=y(()=>{if(!e(x).length)return[];const s=e(x).flatMap(t=>t.data??[]);if(j().newOnBottom?s.sort((t,a)=>t.timestamp.getTime()-a.timestamp.getTime()):s.sort((t,a)=>a.timestamp.getTime()-t.timestamp.getTime()),e(_)){const t=e(_).toLowerCase();return s.filter(a=>a.text.toLowerCase().includes(t)||a.displayName.toLowerCase().includes(t))}return s}),q=y(()=>e(x).some(s=>s.isPending));var S=Ce(),F=l(S);{var H=s=>{var t=be();r(s,t)},I=(s,t)=>{{var a=v=>{var T=ye();r(v,T)},J=(v,T)=>{{var K=p=>{var u=we(),C=l(u);n(u),E(()=>B(C,`Error loading logs: ${L.error.message??""}`)),r(p,u)},R=(p,u)=>{{var C=m=>{var f=xe();r(m,f)},U=m=>{var f=Te(),Q=l(f),V=l(Q),$=l(V);n(V);var O=D(V,2);me(O),n(Q);var W=D(Q,2),ee=l(W);{var se=o=>{var z=Pe();r(o,z)},te=o=>{Le(o,{width:"100%",height:"100%",get itemCount(){return e(P).length},itemSize:24,children:(ae,c)=>{let oe=()=>c==null?void 0:c().style,re=()=>c==null?void 0:c().index;var b=Se(),ie=l(b);ge(ie,{get message(){return e(P)[re()]}}),n(b),E(()=>de(b,oe())),r(ae,b)},$$slots:{default:!0}})};h(ee,o=>{e(q)&&e(P).length===0?o(se):o(te,!1)})}n(W),n(f),E(()=>B($,`Logs for ${e(d)??""} in #${e(g)??""}`)),ce(O,()=>e(_),o=>k(_,o)),r(m,f)};h(p,m=>{!e(w)||e(w).length===0?m(C):m(U,!1)},u)}};h(v,p=>{L.isError?p(K):p(R,!1)},T)}};h(s,v=>{L.isPending?v(a):v(J,!1)},t)}};h(F,s=>{!e(g)||!e(d)?s(H):s(I,!1)})}n(S),r(X,S),ve(),Z()}export{We as component};
