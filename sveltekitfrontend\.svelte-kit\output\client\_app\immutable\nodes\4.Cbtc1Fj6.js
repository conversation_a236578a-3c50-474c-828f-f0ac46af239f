import"../chunks/CWj6FrbW.js";import{V as H,Z as Q,v as I,r as i,W as v,a9 as J,X as n,Y as K,_ as s,a3 as y,aa as M,$ as t,w as V,a0 as g,a1 as w,ab as N,a2 as O}from"../chunks/D8jla-3m.js";import{i as x}from"../chunks/BFoZa56j.js";import{b as T,r as U}from"../chunks/BPG35sX8.js";import{L as $,s as ee}from"../chunks/CYYS3DFc.js";import{b as ae}from"../chunks/CBqDoOLn.js";import{V as se}from"../chunks/2vYeMJ5I.js";var te=v("<p>Searching...</p>"),re=v('<p class="error svelte-1ak0q2n"> </p>'),oe=v("<div><!></div>"),ie=v('<p> </p> <div class="list-container svelte-1ak0q2n"><!></div>',1),le=v('<div class="container svelte-1ak0q2n"><h1>Global Search</h1> <form class="search-form svelte-1ak0q2n"><input type="search" placeholder="Search all logs..." class="svelte-1ak0q2n"/> <button type="submit">Search</button></form> <div class="results svelte-1ak0q2n"><!></div></div>');function he(E,z){H(z,!0);let d=Q(I({})),u=Q("");function A(e){e.preventDefault(),V(d,{q:i(u),users:i(d).users,channels:i(d).channels},!0)}const a=ae(i(d),"global",void 0);var m=le(),c=y(s(m),2),k=s(c);U(k),M(2),t(c);var q=y(c,2),C=s(q);{var D=e=>{var f=te();n(e,f)},F=(e,f)=>{{var G=r=>{var l=re(),h=s(l);t(l),g(()=>w(h,`Error: ${a.error.message??""}`)),n(r,l)},P=(r,l)=>{{var h=_=>{var S=ie(),b=N(S),R=s(b);t(b);var L=y(b,2),W=s(L);se(W,{width:"100%",height:"100%",get itemCount(){return a.data.length},itemSize:24,children:(X,o)=>{let Y=()=>o==null?void 0:o().style,Z=()=>o==null?void 0:o().index;var p=oe();const j=O(()=>a.data[Z()]);var B=s(p);$(B,{get message(){return i(j)}}),t(p),g(()=>ee(p,Y())),n(X,p)},$$slots:{default:!0}}),t(L),g(()=>w(R,`Found ${a.data.length??""} results.`)),n(_,S)};x(r,_=>{a.data&&_(h)},l)}};x(e,r=>{a.isError?r(G):r(P,!1)},f)}};x(C,e=>{a.isPending?e(D):e(F,!1)})}t(q),t(m),J("submit",c,A),T(k,()=>i(u),e=>V(u,e)),n(E,m),K()}export{he as component};
