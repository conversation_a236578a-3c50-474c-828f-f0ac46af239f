import{c as d,a4 as g,u as l,a5 as i,e as m,a6 as b,r as p,a7 as v,A as h,a8 as k}from"./D8jla-3m.js";function x(a=!1){const s=d,e=s.l.u;if(!e)return;let f=()=>v(s.s);if(a){let n=0,t={};const _=h(()=>{let c=!1;const r=s.s;for(const o in r)r[o]!==t[o]&&(t[o]=r[o],c=!0);return c&&n++,n});f=()=>p(_)}e.b.length&&g(()=>{u(s,f),i(e.b)}),l(()=>{const n=m(()=>e.m.map(b));return()=>{for(const t of n)typeof t=="function"&&t()}}),e.a.length&&l(()=>{u(s,f),i(e.a)})}function u(a,s){if(a.l.s)for(const e of a.l.s)p(e);s()}k();export{x as i};
