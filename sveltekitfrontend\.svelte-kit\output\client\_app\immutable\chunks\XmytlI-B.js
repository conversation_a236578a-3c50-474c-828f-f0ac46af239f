var Zt=t=>{throw TypeError(t)};var je=(t,e,n)=>e.has(t)||Zt("Cannot "+n);var A=(t,e,n)=>(je(t,e,"read from private field"),n?n.call(t):e.get(t)),P=(t,e,n)=>e.has(t)?Zt("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n);import{o as Qt,s as $e}from"./50nLBstc.js";import{aV as jt,Z as C,r as O,w as N}from"./D8jla-3m.js";new URL("sveltekit-internal://");function De(t,e){return t==="/"||e==="ignore"?t:e==="never"?t.endsWith("/")?t.slice(0,-1):t:e==="always"&&!t.endsWith("/")?t+"/":t}function Ve(t){return t.split("%25").map(decodeURI).join("%25")}function Be(t){for(const e in t)t[e]=decodeURIComponent(t[e]);return t}function Ut({href:t}){return t.split("#")[0]}function Fe(t,e,n,a=!1){const r=new URL(t);Object.defineProperty(r,"searchParams",{value:new Proxy(r.searchParams,{get(i,o){if(o==="get"||o==="getAll"||o==="has")return l=>(n(l),i[o](l));e();const c=Reflect.get(i,o);return typeof c=="function"?c.bind(i):c}}),enumerable:!0,configurable:!0});const s=["href","pathname","search","toString","toJSON"];a&&s.push("hash");for(const i of s)Object.defineProperty(r,i,{get(){return e(),t[i]},enumerable:!0,configurable:!0});return r}function Me(...t){let e=5381;for(const n of t)if(typeof n=="string"){let a=n.length;for(;a;)e=e*33^n.charCodeAt(--a)}else if(ArrayBuffer.isView(n)){const a=new Uint8Array(n.buffer,n.byteOffset,n.byteLength);let r=a.length;for(;r;)e=e*33^a[--r]}else throw new TypeError("value must be a string or TypedArray");return(e>>>0).toString(36)}function qe(t){const e=atob(t),n=new Uint8Array(e.length);for(let a=0;a<e.length;a++)n[a]=e.charCodeAt(a);return n.buffer}const Ge=window.fetch;window.fetch=(t,e)=>((t instanceof Request?t.method:(e==null?void 0:e.method)||"GET")!=="GET"&&W.delete($t(t)),Ge(t,e));const W=new Map;function He(t,e){const n=$t(t,e),a=document.querySelector(n);if(a!=null&&a.textContent){let{body:r,...s}=JSON.parse(a.textContent);const i=a.getAttribute("data-ttl");return i&&W.set(n,{body:r,init:s,ttl:1e3*Number(i)}),a.getAttribute("data-b64")!==null&&(r=qe(r)),Promise.resolve(new Response(r,s))}return window.fetch(t,e)}function Ke(t,e,n){if(W.size>0){const a=$t(t,n),r=W.get(a);if(r){if(performance.now()<r.ttl&&["default","force-cache","only-if-cached",void 0].includes(n==null?void 0:n.cache))return new Response(r.body,r.init);W.delete(a)}}return window.fetch(e,n)}function $t(t,e){let a=`script[data-sveltekit-fetched][data-url=${JSON.stringify(t instanceof Request?t.url:t)}]`;if(e!=null&&e.headers||e!=null&&e.body){const r=[];e.headers&&r.push([...new Headers(e.headers)].join(",")),e.body&&(typeof e.body=="string"||ArrayBuffer.isView(e.body))&&r.push(e.body),a+=`[data-hash="${Me(...r)}"]`}return a}const We=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function Ye(t){const e=[];return{pattern:t==="/"?/^\/$/:new RegExp(`^${ze(t).map(a=>{const r=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(a);if(r)return e.push({name:r[1],matcher:r[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const s=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(a);if(s)return e.push({name:s[1],matcher:s[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!a)return;const i=a.split(/\[(.+?)\](?!\])/);return"/"+i.map((c,l)=>{if(l%2){if(c.startsWith("x+"))return Lt(String.fromCharCode(parseInt(c.slice(2),16)));if(c.startsWith("u+"))return Lt(String.fromCharCode(...c.slice(2).split("-").map(h=>parseInt(h,16))));const p=We.exec(c),[,u,y,f,m]=p;return e.push({name:f,matcher:m,optional:!!u,rest:!!y,chained:y?l===1&&i[0]==="":!1}),y?"(.*?)":u?"([^/]*)?":"([^/]+?)"}return Lt(c)}).join("")}).join("")}/?$`),params:e}}function Je(t){return!/^\([^)]+\)$/.test(t)}function ze(t){return t.slice(1).split("/").filter(Je)}function Xe(t,e,n){const a={},r=t.slice(1),s=r.filter(o=>o!==void 0);let i=0;for(let o=0;o<e.length;o+=1){const c=e[o];let l=r[o-i];if(c.chained&&c.rest&&i&&(l=r.slice(o-i,o+1).filter(p=>p).join("/"),i=0),l===void 0){c.rest&&(a[c.name]="");continue}if(!c.matcher||n[c.matcher](l)){a[c.name]=l;const p=e[o+1],u=r[o+1];p&&!p.rest&&p.optional&&u&&c.chained&&(i=0),!p&&!u&&Object.keys(a).length===s.length&&(i=0);continue}if(c.optional&&c.chained){i++;continue}return}if(!i)return a}function Lt(t){return t.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function Ze({nodes:t,server_loads:e,dictionary:n,matchers:a}){const r=new Set(e);return Object.entries(n).map(([o,[c,l,p]])=>{const{pattern:u,params:y}=Ye(o),f={id:o,exec:m=>{const h=u.exec(m);if(h)return Xe(h,y,a)},errors:[1,...p||[]].map(m=>t[m]),layouts:[0,...l||[]].map(i),leaf:s(c)};return f.errors.length=f.layouts.length=Math.max(f.errors.length,f.layouts.length),f});function s(o){const c=o<0;return c&&(o=~o),[c,t[o]]}function i(o){return o===void 0?o:[r.has(o),t[o]]}}function pe(t,e=JSON.parse){try{return e(sessionStorage[t])}catch{}}function te(t,e,n=JSON.stringify){const a=n(e);try{sessionStorage[t]=a}catch{}}var le;const x=((le=globalThis.__sveltekit_y66w0h)==null?void 0:le.base)??"";var fe;const Qe=((fe=globalThis.__sveltekit_y66w0h)==null?void 0:fe.assets)??x,tn="1751761836487",ge="sveltekit:snapshot",me="sveltekit:scroll",ye="sveltekit:states",en="sveltekit:pageurl",q="sveltekit:history",X="sveltekit:navigation",V={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},ut=location.origin;function Dt(t){if(t instanceof URL)return t;let e=document.baseURI;if(!e){const n=document.getElementsByTagName("base");e=n.length?n[0].href:document.URL}return new URL(t,e)}function bt(){return{x:pageXOffset,y:pageYOffset}}function M(t,e){return t.getAttribute(`data-sveltekit-${e}`)}const ee={...V,"":V.hover};function we(t){let e=t.assignedSlot??t.parentNode;return(e==null?void 0:e.nodeType)===11&&(e=e.host),e}function _e(t,e){for(;t&&t!==e;){if(t.nodeName.toUpperCase()==="A"&&t.hasAttribute("href"))return t;t=we(t)}}function Pt(t,e,n){let a;try{if(a=new URL(t instanceof SVGAElement?t.href.baseVal:t.href,document.baseURI),n&&a.hash.match(/^#[^/]/)){const o=location.hash.split("#")[1]||"/";a.hash=`#${o}${a.hash}`}}catch{}const r=t instanceof SVGAElement?t.target.baseVal:t.target,s=!a||!!r||At(a,e,n)||(t.getAttribute("rel")||"").split(/\s+/).includes("external"),i=(a==null?void 0:a.origin)===ut&&t.hasAttribute("download");return{url:a,external:s,target:r,download:i}}function dt(t){let e=null,n=null,a=null,r=null,s=null,i=null,o=t;for(;o&&o!==document.documentElement;)a===null&&(a=M(o,"preload-code")),r===null&&(r=M(o,"preload-data")),e===null&&(e=M(o,"keepfocus")),n===null&&(n=M(o,"noscroll")),s===null&&(s=M(o,"reload")),i===null&&(i=M(o,"replacestate")),o=we(o);function c(l){switch(l){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:ee[a??"off"],preload_data:ee[r??"off"],keepfocus:c(e),noscroll:c(n),reload:c(s),replace_state:c(i)}}function ne(t){const e=jt(t);let n=!0;function a(){n=!0,e.update(i=>i)}function r(i){n=!1,e.set(i)}function s(i){let o;return e.subscribe(c=>{(o===void 0||n&&c!==o)&&i(o=c)})}return{notify:a,set:r,subscribe:s}}const ve={v:()=>{}};function nn(){const{set:t,subscribe:e}=jt(!1);let n;async function a(){clearTimeout(n);try{const r=await fetch(`${Qe}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!r.ok)return!1;const i=(await r.json()).version!==tn;return i&&(t(!0),ve.v(),clearTimeout(n)),i}catch{return!1}}return{subscribe:e,check:a}}function At(t,e,n){return t.origin!==ut||!t.pathname.startsWith(e)?!0:n?!(t.pathname===e+"/"||t.pathname===e+"/index.html"||t.protocol==="file:"&&t.pathname.replace(/\/[^/]+\.html?$/,"")===e):!1}function Mn(t){}function re(t){const e=an(t),n=new ArrayBuffer(e.length),a=new DataView(n);for(let r=0;r<n.byteLength;r++)a.setUint8(r,e.charCodeAt(r));return n}const rn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function an(t){t.length%4===0&&(t=t.replace(/==?$/,""));let e="",n=0,a=0;for(let r=0;r<t.length;r++)n<<=6,n|=rn.indexOf(t[r]),a+=6,a===24&&(e+=String.fromCharCode((n&16711680)>>16),e+=String.fromCharCode((n&65280)>>8),e+=String.fromCharCode(n&255),n=a=0);return a===12?(n>>=4,e+=String.fromCharCode(n)):a===18&&(n>>=2,e+=String.fromCharCode((n&65280)>>8),e+=String.fromCharCode(n&255)),e}const on=-1,sn=-2,cn=-3,ln=-4,fn=-5,un=-6;function hn(t,e){if(typeof t=="number")return r(t,!0);if(!Array.isArray(t)||t.length===0)throw new Error("Invalid input");const n=t,a=Array(n.length);function r(s,i=!1){if(s===on)return;if(s===cn)return NaN;if(s===ln)return 1/0;if(s===fn)return-1/0;if(s===un)return-0;if(i)throw new Error("Invalid input");if(s in a)return a[s];const o=n[s];if(!o||typeof o!="object")a[s]=o;else if(Array.isArray(o))if(typeof o[0]=="string"){const c=o[0],l=e==null?void 0:e[c];if(l)return a[s]=l(r(o[1]));switch(c){case"Date":a[s]=new Date(o[1]);break;case"Set":const p=new Set;a[s]=p;for(let f=1;f<o.length;f+=1)p.add(r(o[f]));break;case"Map":const u=new Map;a[s]=u;for(let f=1;f<o.length;f+=2)u.set(r(o[f]),r(o[f+1]));break;case"RegExp":a[s]=new RegExp(o[1],o[2]);break;case"Object":a[s]=Object(o[1]);break;case"BigInt":a[s]=BigInt(o[1]);break;case"null":const y=Object.create(null);a[s]=y;for(let f=1;f<o.length;f+=2)y[o[f]]=r(o[f+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const f=globalThis[c],m=o[1],h=re(m),d=new f(h);a[s]=d;break}case"ArrayBuffer":{const f=o[1],m=re(f);a[s]=m;break}default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(o.length);a[s]=c;for(let l=0;l<o.length;l+=1){const p=o[l];p!==sn&&(c[l]=r(p))}}else{const c={};a[s]=c;for(const l in o){const p=o[l];c[l]=r(p)}}return a[s]}return r(0)}const be=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);[...be];const dn=new Set([...be]);[...dn];function pn(t){return t.filter(e=>e!=null)}class kt{constructor(e,n){this.status=e,typeof n=="string"?this.body={message:n}:n?this.body=n:this.body={message:`Error: ${e}`}}toString(){return JSON.stringify(this.body)}}class Vt{constructor(e,n){this.status=e,this.location=n}}class Bt extends Error{constructor(e,n,a){super(a),this.status=e,this.text=n}}const gn="x-sveltekit-invalidated",mn="x-sveltekit-trailing-slash";function pt(t){return t instanceof kt||t instanceof Bt?t.status:500}function yn(t){return t instanceof Bt?t.text:"Internal Error"}let U,Z,Tt;const wn=Qt.toString().includes("$$")||/function \w+\(\) \{\}/.test(Qt.toString());var et,nt,rt,at,ot,st,it,ct,ue,lt,he,ft,de;wn?(U={data:{},form:null,error:null,params:{},route:{id:null},state:{},status:-1,url:new URL("https://example.com")},Z={current:null},Tt={current:!1}):(U=new(ue=class{constructor(){P(this,et,C({}));P(this,nt,C(null));P(this,rt,C(null));P(this,at,C({}));P(this,ot,C({id:null}));P(this,st,C({}));P(this,it,C(-1));P(this,ct,C(new URL("https://example.com")))}get data(){return O(A(this,et))}set data(e){N(A(this,et),e)}get form(){return O(A(this,nt))}set form(e){N(A(this,nt),e)}get error(){return O(A(this,rt))}set error(e){N(A(this,rt),e)}get params(){return O(A(this,at))}set params(e){N(A(this,at),e)}get route(){return O(A(this,ot))}set route(e){N(A(this,ot),e)}get state(){return O(A(this,st))}set state(e){N(A(this,st),e)}get status(){return O(A(this,it))}set status(e){N(A(this,it),e)}get url(){return O(A(this,ct))}set url(e){N(A(this,ct),e)}},et=new WeakMap,nt=new WeakMap,rt=new WeakMap,at=new WeakMap,ot=new WeakMap,st=new WeakMap,it=new WeakMap,ct=new WeakMap,ue),Z=new(he=class{constructor(){P(this,lt,C(null))}get current(){return O(A(this,lt))}set current(e){N(A(this,lt),e)}},lt=new WeakMap,he),Tt=new(de=class{constructor(){P(this,ft,C(!1))}get current(){return O(A(this,ft))}set current(e){N(A(this,ft),e)}},ft=new WeakMap,de),ve.v=()=>Tt.current=!0);function _n(t){Object.assign(U,t)}const vn="/__data.json",bn=".html__data.json";function An(t){return t.endsWith(".html")?t.replace(/\.html$/,bn):t.replace(/\/$/,"")+vn}const{tick:kn}=$e,En=new Set(["icon","shortcut icon","apple-touch-icon"]),F=pe(me)??{},Q=pe(ge)??{},$={url:ne({}),page:ne({}),navigating:jt(null),updated:nn()};function Ft(t){F[t]=bt()}function Sn(t,e){let n=t+1;for(;F[n];)delete F[n],n+=1;for(n=e+1;Q[n];)delete Q[n],n+=1}function H(t){return location.href=t.href,new Promise(()=>{})}async function Ae(){if("serviceWorker"in navigator){const t=await navigator.serviceWorker.getRegistration(x||"/");t&&await t.update()}}function ae(){}let Mt,Ct,gt,j,Ot,k;const mt=[],yt=[];let L=null;const ht=new Map,ke=new Set,Rn=new Set,Y=new Set;let v={branch:[],error:null,url:null},qt=!1,wt=!1,oe=!0,tt=!1,K=!1,Ee=!1,Gt=!1,Se,S,T,B;const J=new Set;async function Kn(t,e,n){var s,i,o,c;document.URL!==location.href&&(location.href=location.href),k=t,await((i=(s=t.hooks).init)==null?void 0:i.call(s)),Mt=Ze(t),j=document.documentElement,Ot=e,Ct=t.nodes[0],gt=t.nodes[1],Ct(),gt(),S=(o=history.state)==null?void 0:o[q],T=(c=history.state)==null?void 0:c[X],S||(S=T=Date.now(),history.replaceState({...history.state,[q]:S,[X]:T},""));const a=F[S];function r(){a&&(history.scrollRestoration="manual",scrollTo(a.x,a.y))}n?(r(),await jn(Ot,n)):(await z({type:"enter",url:Dt(k.hash?Dn(new URL(location.href)):location.href),replace_state:!0}),r()),Nn()}function In(){mt.length=0,Gt=!1}function Re(t){yt.some(e=>e==null?void 0:e.snapshot)&&(Q[t]=yt.map(e=>{var n;return(n=e==null?void 0:e.snapshot)==null?void 0:n.capture()}))}function Ie(t){var e;(e=Q[t])==null||e.forEach((n,a)=>{var r,s;(s=(r=yt[a])==null?void 0:r.snapshot)==null||s.restore(n)})}function se(){Ft(S),te(me,F),Re(T),te(ge,Q)}async function Ht(t,e,n,a){return z({type:"goto",url:Dt(t),keepfocus:e.keepFocus,noscroll:e.noScroll,replace_state:e.replaceState,state:e.state,redirect_count:n,nav_token:a,accept:()=>{e.invalidateAll&&(Gt=!0),e.invalidate&&e.invalidate.forEach(On)}})}async function Un(t){if(t.id!==(L==null?void 0:L.id)){const e={};J.add(e),L={id:t.id,token:e,promise:Te({...t,preload:e}).then(n=>(J.delete(e),n.type==="loaded"&&n.state.error&&(L=null),n))}}return L.promise}async function xt(t){var n;const e=(n=await St(t,!1))==null?void 0:n.route;e&&await Promise.all([...e.layouts,e.leaf].map(a=>a==null?void 0:a[1]()))}function Ue(t,e,n){var r;v=t.state;const a=document.querySelector("style[data-sveltekit]");if(a&&a.remove(),Object.assign(U,t.props.page),Se=new k.root({target:e,props:{...t.props,stores:$,components:yt},hydrate:n,sync:!1}),Ie(T),n){const s={from:null,to:{params:v.params,route:{id:((r=v.route)==null?void 0:r.id)??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};Y.forEach(i=>i(s))}wt=!0}function _t({url:t,params:e,branch:n,status:a,error:r,route:s,form:i}){let o="never";if(x&&(t.pathname===x||t.pathname===x+"/"))o="always";else for(const f of n)(f==null?void 0:f.slash)!==void 0&&(o=f.slash);t.pathname=De(t.pathname,o),t.search=t.search;const c={type:"loaded",state:{url:t,params:e,branch:n,error:r,route:s},props:{constructors:pn(n).map(f=>f.node.component),page:Jt(U)}};i!==void 0&&(c.props.form=i);let l={},p=!U,u=0;for(let f=0;f<Math.max(n.length,v.branch.length);f+=1){const m=n[f],h=v.branch[f];(m==null?void 0:m.data)!==(h==null?void 0:h.data)&&(p=!0),m&&(l={...l,...m.data},p&&(c.props[`data_${u}`]=l),u+=1)}return(!v.url||t.href!==v.url.href||v.error!==r||i!==void 0&&i!==U.form||p)&&(c.props.page={error:r,params:e,route:{id:(s==null?void 0:s.id)??null},state:{},status:a,url:new URL(t),form:i??null,data:p?l:U.data}),c}async function Kt({loader:t,parent:e,url:n,params:a,route:r,server_data_node:s}){var p,u,y;let i=null,o=!0;const c={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},l=await t();if((p=l.universal)!=null&&p.load){let f=function(...h){for(const d of h){const{href:_}=new URL(d,n);c.dependencies.add(_)}};const m={route:new Proxy(r,{get:(h,d)=>(o&&(c.route=!0),h[d])}),params:new Proxy(a,{get:(h,d)=>(o&&c.params.add(d),h[d])}),data:(s==null?void 0:s.data)??null,url:Fe(n,()=>{o&&(c.url=!0)},h=>{o&&c.search_params.add(h)},k.hash),async fetch(h,d){h instanceof Request&&(d={body:h.method==="GET"||h.method==="HEAD"?void 0:await h.blob(),cache:h.cache,credentials:h.credentials,headers:[...h.headers].length>0?h==null?void 0:h.headers:void 0,integrity:h.integrity,keepalive:h.keepalive,method:h.method,mode:h.mode,redirect:h.redirect,referrer:h.referrer,referrerPolicy:h.referrerPolicy,signal:h.signal,...d});const{resolved:_,promise:R}=Le(h,d,n);return o&&f(_.href),R},setHeaders:()=>{},depends:f,parent(){return o&&(c.parent=!0),e()},untrack(h){o=!1;try{return h()}finally{o=!0}}};i=await l.universal.load.call(null,m)??null}return{node:l,loader:t,server:s,universal:(u=l.universal)!=null&&u.load?{type:"data",data:i,uses:c}:null,data:i??(s==null?void 0:s.data)??null,slash:((y=l.universal)==null?void 0:y.trailingSlash)??(s==null?void 0:s.slash)}}function Le(t,e,n){let a=t instanceof Request?t.url:t;const r=new URL(a,n);r.origin===n.origin&&(a=r.href.slice(n.origin.length));const s=wt?Ke(a,r.href,e):He(a,e);return{resolved:r,promise:s}}function ie(t,e,n,a,r,s){if(Gt)return!0;if(!r)return!1;if(r.parent&&t||r.route&&e||r.url&&n)return!0;for(const i of r.search_params)if(a.has(i))return!0;for(const i of r.params)if(s[i]!==v.params[i])return!0;for(const i of r.dependencies)if(mt.some(o=>o(new URL(i))))return!0;return!1}function Wt(t,e){return(t==null?void 0:t.type)==="data"?t:(t==null?void 0:t.type)==="skip"?e??null:null}function Ln(t,e){if(!t)return new Set(e.searchParams.keys());const n=new Set([...t.searchParams.keys(),...e.searchParams.keys()]);for(const a of n){const r=t.searchParams.getAll(a),s=e.searchParams.getAll(a);r.every(i=>s.includes(i))&&s.every(i=>r.includes(i))&&n.delete(a)}return n}function ce({error:t,url:e,route:n,params:a}){return{type:"loaded",state:{error:t,url:e,route:n,params:a,branch:[]},props:{page:Jt(U),constructors:[]}}}async function Te({id:t,invalidating:e,url:n,params:a,route:r,preload:s}){if((L==null?void 0:L.id)===t)return J.delete(L.token),L.promise;const{errors:i,layouts:o,leaf:c}=r,l=[...o,c];i.forEach(g=>g==null?void 0:g().catch(()=>{})),l.forEach(g=>g==null?void 0:g[1]().catch(()=>{}));let p=null;const u=v.url?t!==vt(v.url):!1,y=v.route?r.id!==v.route.id:!1,f=Ln(v.url,n);let m=!1;const h=l.map((g,w)=>{var D;const b=v.branch[w],E=!!(g!=null&&g[0])&&((b==null?void 0:b.loader)!==g[1]||ie(m,y,u,f,(D=b.server)==null?void 0:D.uses,a));return E&&(m=!0),E});if(h.some(Boolean)){try{p=await Ce(n,h)}catch(g){const w=await G(g,{url:n,params:a,route:{id:t}});return J.has(s)?ce({error:w,url:n,params:a,route:r}):Et({status:pt(g),error:w,url:n,route:r})}if(p.type==="redirect")return p}const d=p==null?void 0:p.nodes;let _=!1;const R=l.map(async(g,w)=>{var Rt;if(!g)return;const b=v.branch[w],E=d==null?void 0:d[w];if((!E||E.type==="skip")&&g[1]===(b==null?void 0:b.loader)&&!ie(_,y,u,f,(Rt=b.universal)==null?void 0:Rt.uses,a))return b;if(_=!0,(E==null?void 0:E.type)==="error")throw E;return Kt({loader:g[1],url:n,params:a,route:r,parent:async()=>{var Xt;const zt={};for(let It=0;It<w;It+=1)Object.assign(zt,(Xt=await R[It])==null?void 0:Xt.data);return zt},server_data_node:Wt(E===void 0&&g[0]?{type:"skip"}:E??null,g[0]?b==null?void 0:b.server:void 0)})});for(const g of R)g.catch(()=>{});const I=[];for(let g=0;g<l.length;g+=1)if(l[g])try{I.push(await R[g])}catch(w){if(w instanceof Vt)return{type:"redirect",location:w.location};if(J.has(s))return ce({error:await G(w,{params:a,url:n,route:{id:r.id}}),url:n,params:a,route:r});let b=pt(w),E;if(d!=null&&d.includes(w))b=w.status??b,E=w.error;else if(w instanceof kt)E=w.body;else{if(await $.updated.check())return await Ae(),await H(n);E=await G(w,{params:a,url:n,route:{id:r.id}})}const D=await Tn(g,I,i);return D?_t({url:n,params:a,branch:I.slice(0,D.idx).concat(D.node),status:b,error:E,route:r}):await Pe(n,{id:r.id},E,b)}else I.push(void 0);return _t({url:n,params:a,branch:I,status:200,error:null,route:r,form:e?void 0:null})}async function Tn(t,e,n){for(;t--;)if(n[t]){let a=t;for(;!e[a];)a-=1;try{return{idx:a+1,node:{node:await n[t](),loader:n[t],data:{},server:null,universal:null}}}catch{continue}}}async function Et({status:t,error:e,url:n,route:a}){const r={};let s=null;if(k.server_loads[0]===0)try{const o=await Ce(n,[!0]);if(o.type!=="data"||o.nodes[0]&&o.nodes[0].type!=="data")throw 0;s=o.nodes[0]??null}catch{(n.origin!==ut||n.pathname!==location.pathname||qt)&&await H(n)}try{const o=await Kt({loader:Ct,url:n,params:r,route:a,parent:()=>Promise.resolve({}),server_data_node:Wt(s)}),c={node:await gt(),loader:gt,universal:null,server:null,data:null};return _t({url:n,params:r,branch:[o,c],status:t,error:e,route:null})}catch(o){if(o instanceof Vt)return Ht(new URL(o.location,location.href),{},0);throw o}}async function xn(t){const e=t.href;if(ht.has(e))return ht.get(e);let n;try{const a=(async()=>{let r=await k.hooks.reroute({url:new URL(t),fetch:async(s,i)=>Le(s,i,t).promise})??t;if(typeof r=="string"){const s=new URL(t);k.hash?s.hash=r:s.pathname=r,r=s}return r})();ht.set(e,a),n=await a}catch{ht.delete(e);return}return n}async function St(t,e){if(t&&!At(t,x,k.hash)){const n=await xn(t);if(!n)return;const a=Pn(n);for(const r of Mt){const s=r.exec(a);if(s)return{id:vt(t),invalidating:e,route:r,params:Be(s),url:t}}}}function Pn(t){return Ve(k.hash?t.hash.replace(/^#/,"").replace(/[?#].+/,""):t.pathname.slice(x.length))||"/"}function vt(t){return(k.hash?t.hash.replace(/^#/,""):t.pathname)+t.search}function xe({url:t,type:e,intent:n,delta:a}){let r=!1;const s=Yt(v,n,t,e);a!==void 0&&(s.navigation.delta=a);const i={...s.navigation,cancel:()=>{r=!0,s.reject(new Error("navigation cancelled"))}};return tt||ke.forEach(o=>o(i)),r?null:s}async function z({type:t,url:e,popped:n,keepfocus:a,noscroll:r,replace_state:s,state:i={},redirect_count:o=0,nav_token:c={},accept:l=ae,block:p=ae}){const u=B;B=c;const y=await St(e,!1),f=t==="enter"?Yt(v,y,e,t):xe({url:e,type:t,delta:n==null?void 0:n.delta,intent:y});if(!f){p(),B===c&&(B=u);return}const m=S,h=T;l(),tt=!0,wt&&f.navigation.type!=="enter"&&$.navigating.set(Z.current=f.navigation);let d=y&&await Te(y);if(!d){if(At(e,x,k.hash))return await H(e);d=await Pe(e,{id:null},await G(new Bt(404,"Not Found",`Not found: ${e.pathname}`),{url:e,params:{},route:{id:null}}),404)}if(e=(y==null?void 0:y.url)||e,B!==c)return f.reject(new Error("navigation aborted")),!1;if(d.type==="redirect")if(o>=20)d=await Et({status:500,error:await G(new Error("Redirect loop"),{url:e,params:{},route:{id:null}}),url:e,route:{id:null}});else return await Ht(new URL(d.location,e).href,{},o+1,c),!1;else d.props.page.status>=400&&await $.updated.check()&&(await Ae(),await H(e));if(In(),Ft(m),Re(h),d.props.page.url.pathname!==e.pathname&&(e.pathname=d.props.page.url.pathname),i=n?n.state:i,!n){const g=s?0:1,w={[q]:S+=g,[X]:T+=g,[ye]:i};(s?history.replaceState:history.pushState).call(history,w,"",e),s||Sn(S,T)}if(L=null,d.props.page.state=i,wt){v=d.state,d.props.page&&(d.props.page.url=e);const g=(await Promise.all(Array.from(Rn,w=>w(f.navigation)))).filter(w=>typeof w=="function");if(g.length>0){let w=function(){g.forEach(b=>{Y.delete(b)})};g.push(w),g.forEach(b=>{Y.add(b)})}Se.$set(d.props),_n(d.props.page),Ee=!0}else Ue(d,Ot,!1);const{activeElement:_}=document;await kn();const R=n?n.scroll:r?bt():null;if(oe){const g=e.hash&&document.getElementById(Ne(e));R?scrollTo(R.x,R.y):g?g.scrollIntoView():scrollTo(0,0)}const I=document.activeElement!==_&&document.activeElement!==document.body;!a&&!I&&$n(e),oe=!0,d.props.page&&Object.assign(U,d.props.page),tt=!1,t==="popstate"&&Ie(T),f.fulfil(void 0),Y.forEach(g=>g(f.navigation)),$.navigating.set(Z.current=null)}async function Pe(t,e,n,a){return t.origin===ut&&t.pathname===location.pathname&&!qt?await Et({status:a,error:n,url:t,route:e}):await H(t)}function Cn(){let t,e,n;j.addEventListener("mousemove",o=>{const c=o.target;clearTimeout(t),t=setTimeout(()=>{s(c,V.hover)},20)});function a(o){o.defaultPrevented||s(o.composedPath()[0],V.tap)}j.addEventListener("mousedown",a),j.addEventListener("touchstart",a,{passive:!0});const r=new IntersectionObserver(o=>{for(const c of o)c.isIntersecting&&(xt(new URL(c.target.href)),r.unobserve(c.target))},{threshold:0});async function s(o,c){const l=_e(o,j),p=l===e&&c>=n;if(!l||p)return;const{url:u,external:y,download:f}=Pt(l,x,k.hash);if(y||f)return;const m=dt(l),h=u&&vt(v.url)===vt(u);if(!(m.reload||h))if(c<=m.preload_data){e=l,n=V.tap;const d=await St(u,!1);if(!d)return;Un(d)}else c<=m.preload_code&&(e=l,n=c,xt(u))}function i(){r.disconnect();for(const o of j.querySelectorAll("a")){const{url:c,external:l,download:p}=Pt(o,x,k.hash);if(l||p)continue;const u=dt(o);u.reload||(u.preload_code===V.viewport&&r.observe(o),u.preload_code===V.eager&&xt(c))}}Y.add(i),i()}function G(t,e){if(t instanceof kt)return t.body;const n=pt(t),a=yn(t);return k.hooks.handleError({error:t,event:e,status:n,message:a})??{message:a}}function Wn(t,e={}){return t=new URL(Dt(t)),t.origin!==ut?Promise.reject(new Error("goto: invalid URL")):Ht(t,e,0)}function On(t){if(typeof t=="function")mt.push(t);else{const{href:e}=new URL(t,location.href);mt.push(n=>n.href===e)}}function Nn(){var e;history.scrollRestoration="manual",addEventListener("beforeunload",n=>{let a=!1;if(se(),!tt){const r=Yt(v,void 0,null,"leave"),s={...r.navigation,cancel:()=>{a=!0,r.reject(new Error("navigation cancelled"))}};ke.forEach(i=>i(s))}a?(n.preventDefault(),n.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&se()}),(e=navigator.connection)!=null&&e.saveData||Cn(),j.addEventListener("click",async n=>{if(n.button||n.which!==1||n.metaKey||n.ctrlKey||n.shiftKey||n.altKey||n.defaultPrevented)return;const a=_e(n.composedPath()[0],j);if(!a)return;const{url:r,external:s,target:i,download:o}=Pt(a,x,k.hash);if(!r)return;if(i==="_parent"||i==="_top"){if(window.parent!==window)return}else if(i&&i!=="_self")return;const c=dt(a);if(!(a instanceof SVGAElement)&&r.protocol!==location.protocol&&!(r.protocol==="https:"||r.protocol==="http:")||o)return;const[p,u]=(k.hash?r.hash.replace(/^#/,""):r.href).split("#"),y=p===Ut(location);if(s||c.reload&&(!y||!u)){xe({url:r,type:"link"})?tt=!0:n.preventDefault();return}if(u!==void 0&&y){const[,f]=v.url.href.split("#");if(f===u){if(n.preventDefault(),u===""||u==="top"&&a.ownerDocument.getElementById("top")===null)window.scrollTo({top:0});else{const m=a.ownerDocument.getElementById(decodeURIComponent(u));m&&(m.scrollIntoView(),m.focus())}return}if(K=!0,Ft(S),t(r),!c.replace_state)return;K=!1}n.preventDefault(),await new Promise(f=>{requestAnimationFrame(()=>{setTimeout(f,0)}),setTimeout(f,100)}),await z({type:"link",url:r,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??r.href===location.href})}),j.addEventListener("submit",n=>{if(n.defaultPrevented)return;const a=HTMLFormElement.prototype.cloneNode.call(n.target),r=n.submitter;if(((r==null?void 0:r.formTarget)||a.target)==="_blank"||((r==null?void 0:r.formMethod)||a.method)!=="get")return;const o=new URL((r==null?void 0:r.hasAttribute("formaction"))&&(r==null?void 0:r.formAction)||a.action);if(At(o,x,!1))return;const c=n.target,l=dt(c);if(l.reload)return;n.preventDefault(),n.stopPropagation();const p=new FormData(c),u=r==null?void 0:r.getAttribute("name");u&&p.append(u,(r==null?void 0:r.getAttribute("value"))??""),o.search=new URLSearchParams(p).toString(),z({type:"form",url:o,keepfocus:l.keepfocus,noscroll:l.noscroll,replace_state:l.replace_state??o.href===location.href})}),addEventListener("popstate",async n=>{var a;if(!Nt){if((a=n.state)!=null&&a[q]){const r=n.state[q];if(B={},r===S)return;const s=F[r],i=n.state[ye]??{},o=new URL(n.state[en]??location.href),c=n.state[X],l=v.url?Ut(location)===Ut(v.url):!1;if(c===T&&(Ee||l)){i!==U.state&&(U.state=i),t(o),F[S]=bt(),s&&scrollTo(s.x,s.y),S=r;return}const u=r-S;await z({type:"popstate",url:o,popped:{state:i,scroll:s,delta:u},accept:()=>{S=r,T=c},block:()=>{history.go(-u)},nav_token:B})}else if(!K){const r=new URL(location.href);t(r),k.hash&&location.reload()}}}),addEventListener("hashchange",()=>{K&&(K=!1,history.replaceState({...history.state,[q]:++S,[X]:T},"",location.href))});for(const n of document.querySelectorAll("link"))En.has(n.rel)&&(n.href=n.href);addEventListener("pageshow",n=>{n.persisted&&$.navigating.set(Z.current=null)});function t(n){v.url=U.url=n,$.page.set(Jt(U)),$.page.notify()}}async function jn(t,{status:e=200,error:n,node_ids:a,params:r,route:s,server_route:i,data:o,form:c}){qt=!0;const l=new URL(location.href);let p;({params:r={},route:s={id:null}}=await St(l,!1)||{}),p=Mt.find(({id:f})=>f===s.id);let u,y=!0;try{const f=a.map(async(h,d)=>{const _=o[d];return _!=null&&_.uses&&(_.uses=Oe(_.uses)),Kt({loader:k.nodes[h],url:l,params:r,route:s,parent:async()=>{const R={};for(let I=0;I<d;I+=1)Object.assign(R,(await f[I]).data);return R},server_data_node:Wt(_)})}),m=await Promise.all(f);if(p){const h=p.layouts;for(let d=0;d<h.length;d++)h[d]||m.splice(d,0,void 0)}u=_t({url:l,params:r,branch:m,status:e,error:n,form:c,route:p??null})}catch(f){if(f instanceof Vt){await H(new URL(f.location,location.href));return}u=await Et({status:pt(f),error:await G(f,{url:l,params:r,route:s}),url:l,route:s}),t.textContent="",y=!1}u.props.page&&(u.props.page.state={}),Ue(u,t,y)}async function Ce(t,e){var s;const n=new URL(t);n.pathname=An(t.pathname),t.pathname.endsWith("/")&&n.searchParams.append(mn,"1"),n.searchParams.append(gn,e.map(i=>i?"1":"0").join(""));const a=window.fetch,r=await a(n.href,{});if(!r.ok){let i;throw(s=r.headers.get("content-type"))!=null&&s.includes("application/json")?i=await r.json():r.status===404?i="Not Found":r.status===500&&(i="Internal Error"),new kt(r.status,i)}return new Promise(async i=>{var y;const o=new Map,c=r.body.getReader(),l=new TextDecoder;function p(f){return hn(f,{...k.decoders,Promise:m=>new Promise((h,d)=>{o.set(m,{fulfil:h,reject:d})})})}let u="";for(;;){const{done:f,value:m}=await c.read();if(f&&!u)break;for(u+=!m&&u?`
`:l.decode(m,{stream:!0});;){const h=u.indexOf(`
`);if(h===-1)break;const d=JSON.parse(u.slice(0,h));if(u=u.slice(h+1),d.type==="redirect")return i(d);if(d.type==="data")(y=d.nodes)==null||y.forEach(_=>{(_==null?void 0:_.type)==="data"&&(_.uses=Oe(_.uses),_.data=p(_.data))}),i(d);else if(d.type==="chunk"){const{id:_,data:R,error:I}=d,g=o.get(_);o.delete(_),I?g.reject(p(I)):g.fulfil(p(R))}}}})}function Oe(t){return{dependencies:new Set((t==null?void 0:t.dependencies)??[]),params:new Set((t==null?void 0:t.params)??[]),parent:!!(t!=null&&t.parent),route:!!(t!=null&&t.route),url:!!(t!=null&&t.url),search_params:new Set((t==null?void 0:t.search_params)??[])}}let Nt=!1;function $n(t){const e=document.querySelector("[autofocus]");if(e)e.focus();else{const n=Ne(t);if(n&&document.getElementById(n)){const{x:r,y:s}=bt();setTimeout(()=>{const i=history.state;Nt=!0,location.replace(`#${n}`),k.hash&&location.replace(t.hash),history.replaceState(i,"",t.hash),scrollTo(r,s),Nt=!1})}else{const r=document.body,s=r.getAttribute("tabindex");r.tabIndex=-1,r.focus({preventScroll:!0,focusVisible:!1}),s!==null?r.setAttribute("tabindex",s):r.removeAttribute("tabindex")}const a=getSelection();if(a&&a.type!=="None"){const r=[];for(let s=0;s<a.rangeCount;s+=1)r.push(a.getRangeAt(s));setTimeout(()=>{if(a.rangeCount===r.length){for(let s=0;s<a.rangeCount;s+=1){const i=r[s],o=a.getRangeAt(s);if(i.commonAncestorContainer!==o.commonAncestorContainer||i.startContainer!==o.startContainer||i.endContainer!==o.endContainer||i.startOffset!==o.startOffset||i.endOffset!==o.endOffset)return}a.removeAllRanges()}})}}}function Yt(t,e,n,a){var c,l;let r,s;const i=new Promise((p,u)=>{r=p,s=u});return i.catch(()=>{}),{navigation:{from:{params:t.params,route:{id:((c=t.route)==null?void 0:c.id)??null},url:t.url},to:n&&{params:(e==null?void 0:e.params)??null,route:{id:((l=e==null?void 0:e.route)==null?void 0:l.id)??null},url:n},willUnload:!e,type:a,complete:i},fulfil:r,reject:s}}function Jt(t){return{data:t.data,error:t.error,form:t.form,params:t.params,route:t.route,state:t.state,status:t.status,url:t.url}}function Dn(t){const e=new URL(t);return e.hash=decodeURIComponent(t.hash),e}function Ne(t){let e;if(k.hash){const[,,n]=t.hash.split("#",3);e=n??""}else e=t.hash.slice(1);return decodeURIComponent(e)}export{Kn as a,Wn as g,Mn as l,U as p,$ as s};
