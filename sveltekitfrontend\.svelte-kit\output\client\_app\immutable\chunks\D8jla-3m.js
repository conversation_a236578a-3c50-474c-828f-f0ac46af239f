var Me=Object.defineProperty;var Le=(t,e,n)=>e in t?Me(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var St=(t,e,n)=>Le(t,typeof e!="symbol"?e+"":e,n);var zt=Array.isArray,Fe=Array.prototype.indexOf,qe=Array.from,je=Object.defineProperty,ut=Object.getOwnPropertyDescriptor,He=Object.getOwnPropertyDescriptors,Ye=Object.prototype,Be=Array.prototype,Xt=Object.getPrototypeOf,Vt=Object.isExtensible;const Z=()=>{};function jn(t){return t()}function Pt(t){for(var e=0;e<t.length;e++)t[e]()}function Hn(t,e){if(Array.isArray(t))return t;if(!(Symbol.iterator in t))return Array.from(t);const n=[];for(const r of t)if(n.push(r),n.length===e)break;return n}const N=2,Zt=4,Et=8,Mt=16,I=32,et=64,Lt=128,x=256,pt=512,O=1024,q=2048,G=4096,Q=8192,Ft=16384,Jt=32768,Qt=65536,Ut=1<<17,Ve=1<<18,te=1<<19,Ot=1<<20,it=Symbol("$state"),Yn=Symbol("legacy props"),Bn=Symbol(""),ee=new class extends Error{constructor(){super(...arguments);St(this,"name","StaleReactionError");St(this,"message","The reaction that called `getAbortSignal()` was re-run or destroyed")}},qt=3,yt=8;function ne(t){return t===this.v}function re(t,e){return t!=t?e==e:t!==e||t!==null&&typeof t=="object"||typeof t=="function"}function ae(t){return!re(t,this.v)}function Ue(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function We(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function $e(t){throw new Error("https://svelte.dev/e/effect_orphan")}function Ge(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function Un(){throw new Error("https://svelte.dev/e/get_abort_signal_outside_reaction")}function Ke(){throw new Error("https://svelte.dev/e/hydration_failed")}function Wn(t){throw new Error("https://svelte.dev/e/lifecycle_legacy_only")}function $n(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function ze(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function Xe(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function Ze(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}let mt=!1;function Gn(){mt=!0}const Kn=1,zn=2,Xn=4,Zn=8,Jn=16,Qn=1,tr=2,er=4,nr=8,rr=16,Je=1,Qe=2,se="[",tn="[!",le="]",J={},T=Symbol(),ar="http://www.w3.org/1999/xhtml";function en(t){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}let y=null;function Wt(t){y=t}function sr(t){return Tt().get(t)}function lr(t,e){return Tt().set(t,e),e}function ur(t){return Tt().has(t)}function ir(){return Tt()}function nn(t,e=!1,n){var r=y={p:y,c:null,d:!1,e:null,m:!1,s:t,x:null,l:null};mt&&!e&&(y.l={s:null,u:null,r1:[],r2:Ht(!1)}),pe(()=>{r.d=!0})}function rn(t){const e=y;if(e!==null){t!==void 0&&(e.x=t);const o=e.e;if(o!==null){var n=h,r=v;e.e=null;try{for(var a=0;a<o.length;a++){var s=o[a];H(s.effect),k(s.reaction),ye(s.fn)}}finally{H(n),k(r)}}y=e.p,e.m=!0}return t||{}}function bt(){return!mt||y!==null&&y.l===null}function Tt(t){return y===null&&en(),y.c??(y.c=new Map(an(y)||void 0))}function an(t){let e=t.p;for(;e!==null;){const n=e.c;if(n!==null)return n;e=e.p}return null}function at(t){if(typeof t!="object"||t===null||it in t)return t;const e=Xt(t);if(e!==Ye&&e!==Be)return t;var n=new Map,r=zt(t),a=P(0),s=v,o=i=>{var u=v;k(s);var l=i();return k(u),l};return r&&n.set("length",P(t.length)),new Proxy(t,{defineProperty(i,u,l){(!("value"in l)||l.configurable===!1||l.enumerable===!1||l.writable===!1)&&ze();var _=n.get(u);return _===void 0?_=o(()=>{var f=P(l.value);return n.set(u,f),f}):M(_,l.value,!0),!0},deleteProperty(i,u){var l=n.get(u);if(l===void 0){if(u in i){const c=o(()=>P(T));n.set(u,c),Nt(a)}}else{if(r&&typeof u=="string"){var _=n.get("length"),f=Number(u);Number.isInteger(f)&&f<_.v&&M(_,f)}M(l,T),Nt(a)}return!0},get(i,u,l){var d;if(u===it)return t;var _=n.get(u),f=u in i;if(_===void 0&&(!f||(d=ut(i,u))!=null&&d.writable)&&(_=o(()=>{var p=at(f?i[u]:T),E=P(p);return E}),n.set(u,_)),_!==void 0){var c=st(_);return c===T?void 0:c}return Reflect.get(i,u,l)},getOwnPropertyDescriptor(i,u){var l=Reflect.getOwnPropertyDescriptor(i,u);if(l&&"value"in l){var _=n.get(u);_&&(l.value=st(_))}else if(l===void 0){var f=n.get(u),c=f==null?void 0:f.v;if(f!==void 0&&c!==T)return{enumerable:!0,configurable:!0,value:c,writable:!0}}return l},has(i,u){var c;if(u===it)return!0;var l=n.get(u),_=l!==void 0&&l.v!==T||Reflect.has(i,u);if(l!==void 0||h!==null&&(!_||(c=ut(i,u))!=null&&c.writable)){l===void 0&&(l=o(()=>{var d=_?at(i[u]):T,p=P(d);return p}),n.set(u,l));var f=st(l);if(f===T)return!1}return _},set(i,u,l,_){var B;var f=n.get(u),c=u in i;if(r&&u==="length")for(var d=l;d<f.v;d+=1){var p=n.get(d+"");p!==void 0?M(p,T):d in i&&(p=o(()=>P(T)),n.set(d+"",p))}if(f===void 0)(!c||(B=ut(i,u))!=null&&B.writable)&&(f=o(()=>P(void 0)),M(f,at(l)),n.set(u,f));else{c=f.v!==T;var E=o(()=>at(l));M(f,E)}var D=Reflect.getOwnPropertyDescriptor(i,u);if(D!=null&&D.set&&D.set.call(_,l),!c){if(r&&typeof u=="string"){var dt=n.get("length"),K=Number(u);Number.isInteger(K)&&K>=dt.v&&M(dt,K+1)}Nt(a)}return!0},ownKeys(i){st(a);var u=Reflect.ownKeys(i).filter(f=>{var c=n.get(f);return c===void 0||c.v!==T});for(var[l,_]of n)_.v!==T&&!(l in i)&&u.push(l);return u},setPrototypeOf(){Xe()}})}function Nt(t,e=1){M(t,t.v+e)}function jt(t){var e=N|q,n=v!==null&&(v.f&N)!==0?v:null;return h===null||n!==null&&(n.f&x)!==0?e|=x:h.f|=te,{ctx:y,deps:null,effects:null,equals:ne,f:e,fn:t,reactions:null,rv:0,v:null,wv:0,parent:n??h,ac:null}}function fr(t){const e=jt(t);return Se(e),e}function or(t){const e=jt(t);return e.equals=ae,e}function ue(t){var e=t.effects;if(e!==null){t.effects=null;for(var n=0;n<e.length;n+=1)$(e[n])}}function sn(t){for(var e=t.parent;e!==null;){if((e.f&N)===0)return e;e=e.parent}return null}function ie(t){var e,n=h;H(sn(t));try{ue(t),e=ke(t)}finally{H(n)}return e}function fe(t){var e=ie(t);if(t.equals(e)||(t.v=e,t.wv=Oe()),!rt){var n=(L||(t.f&x)!==0)&&t.deps!==null?G:O;C(t,n)}}const ot=new Map;function Ht(t,e){var n={f:0,v:t,reactions:null,equals:ne,rv:0,wv:0};return n}function P(t,e){const n=Ht(t);return Se(n),n}function cr(t,e=!1,n=!0){var a;const r=Ht(t);return e||(r.equals=ae),mt&&n&&y!==null&&y.l!==null&&((a=y.l).s??(a.s=[])).push(r),r}function M(t,e,n=!1){v!==null&&(!R||(v.f&Ut)!==0)&&bt()&&(v.f&(N|Mt|Ut))!==0&&!(g!=null&&g[1].includes(t)&&g[0]===v)&&Ze();let r=n?at(e):e;return ln(t,r)}function ln(t,e){if(!t.equals(e)){var n=t.v;rt?ot.set(t,e):ot.set(t,n),t.v=e,(t.f&N)!==0&&((t.f&q)!==0&&ie(t),C(t,(t.f&x)===0?O:G)),t.wv=Oe(),oe(t,q),bt()&&h!==null&&(h.f&O)!==0&&(h.f&(I|et))===0&&(S===null?bn([t]):S.push(t))}return e}function oe(t,e){var n=t.reactions;if(n!==null)for(var r=bt(),a=n.length,s=0;s<a;s++){var o=n[s],i=o.f;(i&q)===0&&(!r&&o===h||(C(o,e),(i&(O|x))!==0&&((i&N)!==0?oe(o,G):Bt(o))))}}function At(t){console.warn("https://svelte.dev/e/hydration_mismatch")}let b=!1;function ht(t){b=t}let w;function j(t){if(t===null)throw At(),J;return w=t}function ce(){return j(Y(w))}function _r(t){if(b){if(Y(w)!==null)throw At(),J;w=t}}function vr(t=1){if(b){for(var e=t,n=w;e--;)n=Y(n);w=n}}function dr(){for(var t=0,e=w;;){if(e.nodeType===yt){var n=e.data;if(n===le){if(t===0)return e;t-=1}else(n===se||n===tn)&&(t+=1)}var r=Y(e);e.remove(),e=r}}function hr(t){if(!t||t.nodeType!==yt)throw At(),J;return t.data}var $t,_e,ve,de;function Rt(){if($t===void 0){$t=window,_e=/Firefox/.test(navigator.userAgent);var t=Element.prototype,e=Node.prototype,n=Text.prototype;ve=ut(e,"firstChild").get,de=ut(e,"nextSibling").get,Vt(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),Vt(n)&&(n.__t=void 0)}}function W(t=""){return document.createTextNode(t)}function tt(t){return ve.call(t)}function Y(t){return de.call(t)}function pr(t,e){if(!b)return tt(t);var n=tt(w);if(n===null)n=w.appendChild(W());else if(e&&n.nodeType!==qt){var r=W();return n==null||n.before(r),j(r),r}return j(n),n}function yr(t,e){if(!b){var n=tt(t);return n instanceof Comment&&n.data===""?Y(n):n}return w}function wr(t,e=1,n=!1){let r=b?w:t;for(var a;e--;)a=r,r=Y(r);if(!b)return r;if(n&&(r==null?void 0:r.nodeType)!==qt){var s=W();return r===null?a==null||a.after(s):r.before(s),j(s),s}return j(r),r}function un(t){t.textContent=""}function he(t){h===null&&v===null&&$e(),v!==null&&(v.f&x)!==0&&h===null&&We(),rt&&Ue()}function fn(t,e){var n=e.last;n===null?e.last=e.first=t:(n.next=t,t.prev=n,e.last=t)}function nt(t,e,n,r=!0){var a=h,s={ctx:y,deps:null,nodes_start:null,nodes_end:null,f:t|q,first:null,fn:e,last:null,next:null,parent:a,b:a&&a.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{Yt(s),s.f|=Jt}catch(u){throw $(s),u}else e!==null&&Bt(s);var o=n&&s.deps===null&&s.first===null&&s.nodes_start===null&&s.teardown===null&&(s.f&(te|Lt))===0;if(!o&&r&&(a!==null&&fn(s,a),v!==null&&(v.f&N)!==0)){var i=v;(i.effects??(i.effects=[])).push(s)}return s}function pe(t){const e=nt(Et,null,!1);return C(e,O),e.teardown=t,e}function gr(t){he();var e=h!==null&&(h.f&I)!==0&&y!==null&&!y.m;if(e){var n=y;(n.e??(n.e=[])).push({fn:t,effect:h,reaction:v})}else{var r=ye(t);return r}}function Er(t){return he(),cn(t)}function on(t){const e=nt(et,t,!0);return(n={})=>new Promise(r=>{n.outro?pn(e,()=>{$(e),r(void 0)}):($(e),r(void 0))})}function ye(t){return nt(Zt,t,!1)}function cn(t){return nt(Et,t,!0)}function mr(t,e=[],n=jt){const r=e.map(n);return _n(()=>t(...r.map(st)))}function _n(t,e=0){var n=nt(Et|Mt|e,t,!0);return n}function vn(t,e=!0){return nt(Et|I,t,!0,e)}function we(t){var e=t.teardown;if(e!==null){const n=rt,r=v;Gt(!0),k(null);try{e.call(null)}finally{Gt(n),k(r)}}}function ge(t,e=!1){var a;var n=t.first;for(t.first=t.last=null;n!==null;){(a=n.ac)==null||a.abort(ee);var r=n.next;(n.f&et)!==0?n.parent=null:$(n,e),n=r}}function dn(t){for(var e=t.first;e!==null;){var n=e.next;(e.f&I)===0&&$(e),e=n}}function $(t,e=!0){var n=!1;(e||(t.f&Ve)!==0)&&t.nodes_start!==null&&t.nodes_end!==null&&(hn(t.nodes_start,t.nodes_end),n=!0),ge(t,e&&!n),gt(t,0),C(t,Ft);var r=t.transitions;if(r!==null)for(const s of r)s.stop();we(t);var a=t.parent;a!==null&&a.first!==null&&Ee(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=t.ac=null}function hn(t,e){for(;t!==null;){var n=t===e?null:Y(t);t.remove(),t=n}}function Ee(t){var e=t.parent,n=t.prev,r=t.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),e!==null&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function pn(t,e){var n=[];me(t,n,!0),yn(n,()=>{$(t),e&&e()})}function yn(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var a of t)a.out(r)}else e()}function me(t,e,n){if((t.f&Q)===0){if(t.f^=Q,t.transitions!==null)for(const o of t.transitions)(o.is_global||n)&&e.push(o);for(var r=t.first;r!==null;){var a=r.next,s=(r.f&Qt)!==0||(r.f&I)!==0;me(r,e,s?n:!1),r=a}}}function br(t){be(t,!0)}function be(t,e){if((t.f&Q)!==0){t.f^=Q;for(var n=t.first;n!==null;){var r=n.next,a=(n.f&Qt)!==0||(n.f&I)!==0;be(n,a?e:!1),n=r}if(t.transitions!==null)for(const s of t.transitions)(s.is_global||e)&&s.in()}}const wn=typeof requestIdleCallback>"u"?t=>setTimeout(t,1):requestIdleCallback;let ct=[],_t=[];function Te(){var t=ct;ct=[],Pt(t)}function Ae(){var t=_t;_t=[],Pt(t)}function gn(t){ct.length===0&&queueMicrotask(Te),ct.push(t)}function Tr(t){_t.length===0&&wn(Ae),_t.push(t)}function En(){ct.length>0&&Te(),_t.length>0&&Ae()}function mn(t){var e=h;if((e.f&Jt)===0){if((e.f&Lt)===0)throw t;e.fn(t)}else xe(t,e)}function xe(t,e){for(;e!==null;){if((e.f&Lt)!==0)try{e.b.error(t);return}catch{}e=e.parent}throw t}let V=!1,vt=null,U=!1,rt=!1;function Gt(t){rt=t}let ft=[];let v=null,R=!1;function k(t){v=t}let h=null;function H(t){h=t}let g=null;function Se(t){v!==null&&v.f&Ot&&(g===null?g=[v,[t]]:g[1].push(t))}let m=null,A=0,S=null;function bn(t){S=t}let Ne=1,wt=0,L=!1;function Oe(){return++Ne}function xt(t){var f;var e=t.f;if((e&q)!==0)return!0;if((e&G)!==0){var n=t.deps,r=(e&x)!==0;if(n!==null){var a,s,o=(e&pt)!==0,i=r&&h!==null&&!L,u=n.length;if(o||i){var l=t,_=l.parent;for(a=0;a<u;a++)s=n[a],(o||!((f=s==null?void 0:s.reactions)!=null&&f.includes(l)))&&(s.reactions??(s.reactions=[])).push(l);o&&(l.f^=pt),i&&_!==null&&(_.f&x)===0&&(l.f^=x)}for(a=0;a<u;a++)if(s=n[a],xt(s)&&fe(s),s.wv>t.wv)return!0}(!r||h!==null&&!L)&&C(t,O)}return!1}function Re(t,e,n=!0){var r=t.reactions;if(r!==null)for(var a=0;a<r.length;a++){var s=r[a];g!=null&&g[1].includes(t)&&g[0]===v||((s.f&N)!==0?Re(s,e,!1):e===s&&(n?C(s,q):(s.f&O)!==0&&C(s,G),Bt(s)))}}function ke(t){var d;var e=m,n=A,r=S,a=v,s=L,o=g,i=y,u=R,l=t.f;m=null,A=0,S=null,L=(l&x)!==0&&(R||!U||v===null),v=(l&(I|et))===0?t:null,g=null,Wt(t.ctx),R=!1,wt++,t.f|=Ot,t.ac!==null&&(t.ac.abort(ee),t.ac=null);try{var _=(0,t.fn)(),f=t.deps;if(m!==null){var c;if(gt(t,A),f!==null&&A>0)for(f.length=A+m.length,c=0;c<m.length;c++)f[A+c]=m[c];else t.deps=f=m;if(!L||(l&N)!==0&&t.reactions!==null)for(c=A;c<f.length;c++)((d=f[c]).reactions??(d.reactions=[])).push(t)}else f!==null&&A<f.length&&(gt(t,A),f.length=A);if(bt()&&S!==null&&!R&&f!==null&&(t.f&(N|G|q))===0)for(c=0;c<S.length;c++)Re(S[c],t);return a!==null&&a!==t&&(wt++,S!==null&&(r===null?r=S:r.push(...S))),_}catch(p){mn(p)}finally{m=e,A=n,S=r,v=a,L=s,g=o,Wt(i),R=u,t.f^=Ot}}function Tn(t,e){let n=e.reactions;if(n!==null){var r=Fe.call(n,t);if(r!==-1){var a=n.length-1;a===0?n=e.reactions=null:(n[r]=n[a],n.pop())}}n===null&&(e.f&N)!==0&&(m===null||!m.includes(e))&&(C(e,G),(e.f&(x|pt))===0&&(e.f^=pt),ue(e),gt(e,0))}function gt(t,e){var n=t.deps;if(n!==null)for(var r=e;r<n.length;r++)Tn(t,n[r])}function Yt(t){var e=t.f;if((e&Ft)===0){C(t,O);var n=h,r=U;h=t,U=!0;try{(e&Mt)!==0?dn(t):ge(t),we(t);var a=ke(t);t.teardown=typeof a=="function"?a:null,t.wv=Ne;var s}finally{U=r,h=n}}}function An(){try{Ge()}catch(t){if(vt!==null)xe(t,vt);else throw t}}function kt(){var t=U;try{var e=0;for(U=!0;ft.length>0;){e++>1e3&&An();var n=ft,r=n.length;ft=[];for(var a=0;a<r;a++){var s=Sn(n[a]);xn(s)}ot.clear()}}finally{V=!1,U=t,vt=null}}function xn(t){var e=t.length;if(e!==0)for(var n=0;n<e;n++){var r=t[n];(r.f&(Ft|Q))===0&&xt(r)&&(Yt(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?Ee(r):r.fn=null))}}function Bt(t){V||(V=!0,queueMicrotask(kt));for(var e=vt=t;e.parent!==null;){e=e.parent;var n=e.f;if((n&(et|I))!==0){if((n&O)===0)return;e.f^=O}}ft.push(e)}function Sn(t){for(var e=[],n=t;n!==null;){var r=n.f,a=(r&(I|et))!==0,s=a&&(r&O)!==0;if(!s&&(r&Q)===0){(r&Zt)!==0?e.push(n):a?n.f^=O:xt(n)&&Yt(n);var o=n.first;if(o!==null){n=o;continue}}var i=n.parent;for(n=n.next;n===null&&i!==null;)n=i.next,i=i.parent}return e}function Nn(t){var e;for(t&&(V=!0,kt(),V=!0,e=t());;){if(En(),ft.length===0)return V=!1,vt=null,e;V=!0,kt()}}async function Ar(){await Promise.resolve(),Nn()}function st(t){var e=t.f,n=(e&N)!==0;if(v!==null&&!R){if(!(g!=null&&g[1].includes(t))||g[0]!==v){var r=v.deps;t.rv<wt&&(t.rv=wt,m===null&&r!==null&&r[A]===t?A++:m===null?m=[t]:(!L||!m.includes(t))&&m.push(t))}}else if(n&&t.deps===null&&t.effects===null){var a=t,s=a.parent;s!==null&&(s.f&x)===0&&(a.f^=x)}return n&&(a=t,xt(a)&&fe(a)),rt&&ot.has(t)?ot.get(t):t.v}function On(t){var e=R;try{return R=!0,t()}finally{R=e}}const Rn=-7169;function C(t,e){t.f=t.f&Rn|e}function xr(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(it in t)Ct(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];typeof n=="object"&&n&&it in n&&Ct(n)}}}function Ct(t,e=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!e.has(t)){e.add(t),t instanceof Date&&t.getTime();for(let r in t)try{Ct(t[r],e)}catch{}const n=Xt(t);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=He(n);for(let a in r){const s=r[a].get;if(s)try{s.call(t)}catch{}}}}}const kn=["touchstart","touchmove"];function Cn(t){return kn.includes(t)}let Kt=!1;function In(){Kt||(Kt=!0,document.addEventListener("reset",t=>{Promise.resolve().then(()=>{var e;if(!t.defaultPrevented)for(const n of t.target.elements)(e=n.__on_r)==null||e.call(n)})},{capture:!0}))}function Ce(t){var e=v,n=h;k(null),H(null);try{return t()}finally{k(e),H(n)}}function Sr(t,e,n,r=n){t.addEventListener(e,()=>Ce(n));const a=t.__on_r;a?t.__on_r=()=>{a(),r(!0)}:t.__on_r=()=>r(!0),In()}const Ie=new Set,It=new Set;function Dn(t,e,n,r={}){function a(s){if(r.capture||lt.call(e,s),!s.cancelBubble)return Ce(()=>n==null?void 0:n.call(this,s))}return t.startsWith("pointer")||t.startsWith("touch")||t==="wheel"?gn(()=>{e.addEventListener(t,a,r)}):e.addEventListener(t,a,r),a}function Nr(t,e,n,r,a){var s={capture:r,passive:a},o=Dn(t,e,n,s);(e===document.body||e===window||e===document||e instanceof HTMLMediaElement)&&pe(()=>{e.removeEventListener(t,o,s)})}function Or(t){for(var e=0;e<t.length;e++)Ie.add(t[e]);for(var n of It)n(t)}function lt(t){var K;var e=this,n=e.ownerDocument,r=t.type,a=((K=t.composedPath)==null?void 0:K.call(t))||[],s=a[0]||t.target,o=0,i=t.__root;if(i){var u=a.indexOf(i);if(u!==-1&&(e===document||e===window)){t.__root=e;return}var l=a.indexOf(e);if(l===-1)return;u<=l&&(o=u)}if(s=a[o]||t.target,s!==e){je(t,"currentTarget",{configurable:!0,get(){return s||n}});var _=v,f=h;k(null),H(null);try{for(var c,d=[];s!==null;){var p=s.assignedSlot||s.parentNode||s.host||null;try{var E=s["__"+r];if(E!=null&&(!s.disabled||t.target===s))if(zt(E)){var[D,...dt]=E;D.apply(s,[t,...dt])}else E.call(s,t)}catch(B){c?d.push(B):c=B}if(t.cancelBubble||p===e||p===null)break;s=p}if(c){for(let B of d)queueMicrotask(()=>{throw B});throw c}}finally{t.__root=e,delete t.currentTarget,k(_),H(f)}}}function Pn(t){var e=document.createElement("template");return e.innerHTML=t.replaceAll("<!>","<!---->"),e.content}function F(t,e){var n=h;n.nodes_start===null&&(n.nodes_start=t,n.nodes_end=e)}function Rr(t,e){var n=(e&Je)!==0,r=(e&Qe)!==0,a,s=!t.startsWith("<!>");return()=>{if(b)return F(w,null),w;a===void 0&&(a=Pn(s?t:"<!>"+t),n||(a=tt(a)));var o=r||_e?document.importNode(a,!0):a.cloneNode(!0);if(n){var i=tt(o),u=o.lastChild;F(i,u)}else F(o,o);return o}}function kr(t=""){if(!b){var e=W(t+"");return F(e,e),e}var n=w;return n.nodeType!==qt&&(n.before(n=W()),j(n)),F(n,n),n}function Cr(){if(b)return F(w,null),w;var t=document.createDocumentFragment(),e=document.createComment(""),n=W();return t.append(e,n),F(e,n),t}function Ir(t,e){if(b){h.nodes_end=w,ce();return}t!==null&&t.before(e)}function Dr(t,e){var n=e==null?"":typeof e=="object"?e+"":e;n!==(t.__t??(t.__t=t.nodeValue))&&(t.__t=n,t.nodeValue=n+"")}function Mn(t,e){return De(t,e)}function Pr(t,e){Rt(),e.intro=e.intro??!1;const n=e.target,r=b,a=w;try{for(var s=tt(n);s&&(s.nodeType!==yt||s.data!==se);)s=Y(s);if(!s)throw J;ht(!0),j(s),ce();const o=De(t,{...e,anchor:s});if(w===null||w.nodeType!==yt||w.data!==le)throw At(),J;return ht(!1),o}catch(o){if(o===J)return e.recover===!1&&Ke(),Rt(),un(n),ht(!1),Mn(t,e);throw o}finally{ht(r),j(a)}}const z=new Map;function De(t,{target:e,anchor:n,props:r={},events:a,context:s,intro:o=!0}){Rt();var i=new Set,u=f=>{for(var c=0;c<f.length;c++){var d=f[c];if(!i.has(d)){i.add(d);var p=Cn(d);e.addEventListener(d,lt,{passive:p});var E=z.get(d);E===void 0?(document.addEventListener(d,lt,{passive:p}),z.set(d,1)):z.set(d,E+1)}}};u(qe(Ie)),It.add(u);var l=void 0,_=on(()=>{var f=n??e.appendChild(W());return vn(()=>{if(s){nn({});var c=y;c.c=s}a&&(r.$$events=a),b&&F(f,null),l=t(f,r)||{},b&&(h.nodes_end=w),s&&rn()}),()=>{var p;for(var c of i){e.removeEventListener(c,lt);var d=z.get(c);--d===0?(document.removeEventListener(c,lt),z.delete(c)):z.set(c,d)}It.delete(u),f!==n&&((p=f.parentNode)==null||p.removeChild(f))}});return Dt.set(l,_),l}let Dt=new WeakMap;function Mr(t,e){const n=Dt.get(t);return n?(Dt.delete(t),n(e)):Promise.resolve()}function Pe(t,e,n){if(t==null)return e(void 0),n&&n(void 0),Z;const r=On(()=>t.subscribe(e,n));return r.unsubscribe?()=>r.unsubscribe():r}const X=[];function Ln(t,e){return{subscribe:Fn(t,e).subscribe}}function Fn(t,e=Z){let n=null;const r=new Set;function a(i){if(re(t,i)&&(t=i,n)){const u=!X.length;for(const l of r)l[1](),X.push(l,t);if(u){for(let l=0;l<X.length;l+=2)X[l][0](X[l+1]);X.length=0}}}function s(i){a(i(t))}function o(i,u=Z){const l=[i,u];return r.add(l),r.size===1&&(n=e(a,s)||Z),i(t),()=>{r.delete(l),r.size===0&&n&&(n(),n=null)}}return{set:a,update:s,subscribe:o}}function Lr(t,e,n){const r=!Array.isArray(t),a=r?[t]:t;if(!a.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const s=e.length<2;return Ln(n,(o,i)=>{let u=!1;const l=[];let _=0,f=Z;const c=()=>{if(_)return;f();const p=e(r?l[0]:l,o,i);s?o(p):f=typeof p=="function"?p:Z},d=a.map((p,E)=>Pe(p,D=>{l[E]=D,_&=~(1<<E),u&&c()},()=>{_|=1<<E}));return u=!0,c(),function(){Pt(d),f(),u=!1}})}function Fr(t){let e;return Pe(t,n=>e=n)(),e}export{_r as $,jt as A,Qn as B,rr as C,b as D,ce as E,Pn as F,F as G,pe as H,w as I,tt as J,_n as K,Yn as L,Qt as M,vn as N,Z as O,er as P,$ as Q,ye as R,it as S,cn as T,gn as U,nn as V,Rr as W,Ir as X,rn as Y,P as Z,pr as _,v as a,mr as a0,Dr as a1,fr as a2,wr as a3,Er as a4,Pt as a5,jn as a6,xr as a7,Gn as a8,Nr as a9,me as aA,un as aB,yn as aC,Kn as aD,Jn as aE,Y as aF,Xn as aG,Zn as aH,Tr as aI,In as aJ,Bn as aK,ar as aL,Xt as aM,He as aN,bt as aO,Sr as aP,kr as aQ,Ye as aR,Hn as aS,Ln as aT,Lr as aU,Fn as aV,hn as aW,At as aX,J as aY,vr as aa,yr as ab,Or as ac,Cr as ad,hr as ae,se as af,tn as ag,dr as ah,j as ai,ht as aj,br as ak,pn as al,T as am,je as an,cr as ao,Pe as ap,Fr as aq,W as ar,yt as as,le as at,Q as au,qe as av,h as aw,ln as ax,Ht as ay,zn as az,Wn as b,y as c,mt as d,On as e,Nn as f,Un as g,ir as h,zt as i,sr as j,ur as k,en as l,Pr as m,Mn as n,Mr as o,ut as p,$n as q,st as r,lr as s,Ar as t,gr as u,at as v,M as w,or as x,tr as y,nr as z};
