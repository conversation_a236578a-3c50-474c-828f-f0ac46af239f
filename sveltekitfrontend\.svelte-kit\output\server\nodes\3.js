

export const index = 3;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/analytics/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/3.C4puffwM.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/COxqGmGD.js","_app/immutable/chunks/D8jla-3m.js","_app/immutable/chunks/BFoZa56j.js","_app/immutable/chunks/CBqDoOLn.js","_app/immutable/chunks/CzvPouDc.js","_app/immutable/chunks/Ce_2pv8Y.js"];
export const stylesheets = ["_app/immutable/assets/3.9Nao0Ehk.css"];
export const fonts = [];
