import"../chunks/CWj6FrbW.js";import{i as h}from"../chunks/COxqGmGD.js";import{V as g,W as l,ab as v,a0 as _,X as d,Y as x,_ as s,$ as o,a3 as $,a1 as p}from"../chunks/D8jla-3m.js";import{s as b,p as i}from"../chunks/XmytlI-B.js";const k={get error(){return i.error},get status(){return i.status}};b.updated.check;const m=k;var E=l("<h1> </h1> <p> </p>",1);function j(n,c){g(c,!1),h();var r=E(),t=v(r),f=s(t,!0);o(t);var a=$(t,2),u=s(a,!0);o(a),_(()=>{var e;p(f,m.status),p(u,(e=m.error)==null?void 0:e.message)}),d(n,r),x()}export{j as component};
