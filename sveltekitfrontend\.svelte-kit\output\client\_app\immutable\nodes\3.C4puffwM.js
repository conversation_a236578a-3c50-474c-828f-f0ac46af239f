var Bo=Object.defineProperty;var Vo=(i,t,e)=>t in i?Bo(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var k=(i,t,e)=>Vo(i,typeof t!="symbol"?t+"":t,e);import"../chunks/CWj6FrbW.js";import{i as Wo}from"../chunks/COxqGmGD.js";import{V as Ui,u as Fn,r as rt,a2 as zn,W as ne,X as Jt,Y as Xi,a3 as xt,_ as st,$ as nt,a0 as hs,a1 as Te,ab as No,x as ds}from"../chunks/D8jla-3m.js";import{i as Gt,s as Ho,a as jo}from"../chunks/BFoZa56j.js";import{t as Yo,c as $o,d as Uo}from"../chunks/CBqDoOLn.js";import{b as Bn}from"../chunks/CzvPouDc.js";import{s as Xo}from"../chunks/Ce_2pv8Y.js";function Ko(i,t,e){const s=Yo(i,e==null?void 0:e.in);return isNaN(t)?$o(i,NaN):(s.setDate(s.getDate()+t),s)}function qo(i,t,e){return Ko(i,-30,e)}/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function Ae(i){return i+.5|0}const Ct=(i,t,e)=>Math.max(Math.min(i,e),t);function he(i){return Ct(Ae(i*2.55),0,255)}function Lt(i){return Ct(Ae(i*255),0,255)}function Mt(i){return Ct(Ae(i/2.55)/100,0,1)}function fs(i){return Ct(Ae(i*100),0,100)}const at={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Oi=[..."0123456789ABCDEF"],Go=i=>Oi[i&15],Zo=i=>Oi[(i&240)>>4]+Oi[i&15],Le=i=>(i&240)>>4===(i&15),Qo=i=>Le(i.r)&&Le(i.g)&&Le(i.b)&&Le(i.a);function Jo(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&at[i[1]]*17,g:255&at[i[2]]*17,b:255&at[i[3]]*17,a:t===5?at[i[4]]*17:255}:(t===7||t===9)&&(e={r:at[i[1]]<<4|at[i[2]],g:at[i[3]]<<4|at[i[4]],b:at[i[5]]<<4|at[i[6]],a:t===9?at[i[7]]<<4|at[i[8]]:255})),e}const ta=(i,t)=>i<255?t(i):"";function ea(i){var t=Qo(i)?Go:Zo;return i?"#"+t(i.r)+t(i.g)+t(i.b)+ta(i.a,t):void 0}const ia=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Vn(i,t,e){const s=t*Math.min(e,1-e),n=(o,a=(o+i/30)%12)=>e-s*Math.max(Math.min(a-3,9-a,1),-1);return[n(0),n(8),n(4)]}function sa(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function na(i,t,e){const s=Vn(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function oa(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function Ki(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),a=Math.min(e,s,n),r=(o+a)/2;let l,c,h;return o!==a&&(h=o-a,c=r>.5?h/(2-o-a):h/(o+a),l=oa(e,s,n,h,o),l=l*60+.5),[l|0,c||0,r]}function qi(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(Lt)}function Gi(i,t,e){return qi(Vn,i,t,e)}function aa(i,t,e){return qi(na,i,t,e)}function ra(i,t,e){return qi(sa,i,t,e)}function Wn(i){return(i%360+360)%360}function la(i){const t=ia.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?he(+t[5]):Lt(+t[5]));const n=Wn(+t[2]),o=+t[3]/100,a=+t[4]/100;return t[1]==="hwb"?s=aa(n,o,a):t[1]==="hsv"?s=ra(n,o,a):s=Gi(n,o,a),{r:s[0],g:s[1],b:s[2],a:e}}function ca(i,t){var e=Ki(i);e[0]=Wn(e[0]+t),e=Gi(e),i.r=e[0],i.g=e[1],i.b=e[2]}function ha(i){if(!i)return;const t=Ki(i),e=t[0],s=fs(t[1]),n=fs(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${Mt(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const us={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},gs={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function da(){const i={},t=Object.keys(gs),e=Object.keys(us);let s,n,o,a,r;for(s=0;s<t.length;s++){for(a=r=t[s],n=0;n<e.length;n++)o=e[n],r=r.replace(o,us[o]);o=parseInt(gs[a],16),i[r]=[o>>16&255,o>>8&255,o&255]}return i}let Re;function fa(i){Re||(Re=da(),Re.transparent=[0,0,0,0]);const t=Re[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const ua=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function ga(i){const t=ua.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const a=+t[7];e=t[8]?he(a):Ct(a*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?he(s):Ct(s,0,255)),n=255&(t[4]?he(n):Ct(n,0,255)),o=255&(t[6]?he(o):Ct(o,0,255)),{r:s,g:n,b:o,a:e}}}function pa(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${Mt(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const mi=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Zt=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function ma(i,t,e){const s=Zt(Mt(i.r)),n=Zt(Mt(i.g)),o=Zt(Mt(i.b));return{r:Lt(mi(s+e*(Zt(Mt(t.r))-s))),g:Lt(mi(n+e*(Zt(Mt(t.g))-n))),b:Lt(mi(o+e*(Zt(Mt(t.b))-o))),a:i.a+e*(t.a-i.a)}}function Ee(i,t,e){if(i){let s=Ki(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=Gi(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function Nn(i,t){return i&&Object.assign(t||{},i)}function ps(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=Lt(i[3]))):(t=Nn(i,{r:0,g:0,b:0,a:1}),t.a=Lt(t.a)),t}function ba(i){return i.charAt(0)==="r"?ga(i):la(i)}class ve{constructor(t){if(t instanceof ve)return t;const e=typeof t;let s;e==="object"?s=ps(t):e==="string"&&(s=Jo(t)||fa(t)||ba(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=Nn(this._rgb);return t&&(t.a=Mt(t.a)),t}set rgb(t){this._rgb=ps(t)}rgbString(){return this._valid?pa(this._rgb):void 0}hexString(){return this._valid?ea(this._rgb):void 0}hslString(){return this._valid?ha(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const a=e===o?.5:e,r=2*a-1,l=s.a-n.a,c=((r*l===-1?r:(r+l)/(1+r*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=a*s.a+(1-a)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=ma(this._rgb,t._rgb,e)),this}clone(){return new ve(this.rgb)}alpha(t){return this._rgb.a=Lt(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=Ae(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Ee(this._rgb,2,t),this}darken(t){return Ee(this._rgb,2,-t),this}saturate(t){return Ee(this._rgb,1,t),this}desaturate(t){return Ee(this._rgb,1,-t),this}rotate(t){return ca(this._rgb,t),this}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function _t(){}const xa=(()=>{let i=0;return()=>i++})();function O(i){return i==null}function W(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function L(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function H(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function ot(i,t){return H(i)?i:t}function D(i,t){return typeof i>"u"?t:i}const _a=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100:+i/t,Hn=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function z(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function I(i,t,e,s){let n,o,a;if(W(i))for(o=i.length,n=0;n<o;n++)t.call(e,i[n],n);else if(L(i))for(a=Object.keys(i),o=a.length,n=0;n<o;n++)t.call(e,i[a[n]],a[n])}function ei(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function ii(i){if(W(i))return i.map(ii);if(L(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=ii(i[e[n]]);return t}return i}function jn(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function ya(i,t,e,s){if(!jn(i))return;const n=t[i],o=e[i];L(n)&&L(o)?Me(n,o,s):t[i]=ii(o)}function Me(i,t,e){const s=W(t)?t:[t],n=s.length;if(!L(i))return i;e=e||{};const o=e.merger||ya;let a;for(let r=0;r<n;++r){if(a=s[r],!L(a))continue;const l=Object.keys(a);for(let c=0,h=l.length;c<h;++c)o(l[c],i,a,e)}return i}function me(i,t){return Me(i,t,{merger:va})}function va(i,t,e){if(!jn(i))return;const s=t[i],n=e[i];L(s)&&L(n)?me(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=ii(n))}const ms={"":i=>i,x:i=>i.x,y:i=>i.y};function Ma(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function ka(i){const t=Ma(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function Rt(i,t){return(ms[t]||(ms[t]=ka(t)))(i)}function Zi(i){return i.charAt(0).toUpperCase()+i.slice(1)}const ke=i=>typeof i<"u",Et=i=>typeof i=="function",bs=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function Sa(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const R=Math.PI,V=2*R,wa=V+R,si=Number.POSITIVE_INFINITY,Pa=R/180,Y=R/2,zt=R/4,xs=R*2/3,At=Math.log10,mt=Math.sign;function be(i,t,e){return Math.abs(i-t)<e}function _s(i){const t=Math.round(i);i=be(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(At(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function Da(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function Ca(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function ee(i){return!Ca(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function Aa(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function Yn(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function lt(i){return i*(R/180)}function Qi(i){return i*(180/R)}function ys(i){if(!H(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function $n(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*R&&(o+=V),{angle:o,distance:n}}function Ti(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function Oa(i,t){return(i-t+wa)%V-R}function Z(i){return(i%V+V)%V}function Se(i,t,e,s){const n=Z(i),o=Z(t),a=Z(e),r=Z(o-n),l=Z(a-n),c=Z(n-o),h=Z(n-a);return n===o||n===a||s&&o===a||r>l&&c<h}function K(i,t,e){return Math.max(t,Math.min(e,i))}function Ta(i){return K(i,-32768,32767)}function kt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function Ji(i,t,e){e=e||(a=>i[a]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const St=(i,t,e,s)=>Ji(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),La=(i,t,e)=>Ji(i,e,s=>i[s][t]>=e);function Ra(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const Un=["push","pop","shift","splice","unshift"];function Ea(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Un.forEach(e=>{const s="_onData"+Zi(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const a=n.apply(this,o);return i._chartjs.listeners.forEach(r=>{typeof r[s]=="function"&&r[s](...o)}),a}})})}function vs(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(Un.forEach(o=>{delete i[o]}),delete i._chartjs)}function Xn(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const Kn=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function qn(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Kn.call(window,()=>{s=!1,i.apply(t,e)}))}}function Ia(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const ts=i=>i==="start"?"left":i==="end"?"right":"center",G=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,Fa=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t;function Gn(i,t,e){const s=t.length;let n=0,o=s;if(i._sorted){const{iScale:a,vScale:r,_parsed:l}=i,c=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null,h=a.axis,{min:d,max:f,minDefined:u,maxDefined:g}=a.getUserBounds();if(u){if(n=Math.min(St(l,h,d).lo,e?s:St(t,h,a.getPixelForValue(d)).lo),c){const p=l.slice(0,n+1).reverse().findIndex(m=>!O(m[r.axis]));n-=Math.max(0,p)}n=K(n,0,s-1)}if(g){let p=Math.max(St(l,a.axis,f,!0).hi+1,e?0:St(t,h,a.getPixelForValue(f),!0).hi+1);if(c){const m=l.slice(p-1).findIndex(b=>!O(b[r.axis]));p+=Math.max(0,m)}o=K(p,n,s)-n}else o=s-n}return{start:n,count:o}}function Zn(i){const{xScale:t,yScale:e,_scaleRanges:s}=i,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!s)return i._scaleRanges=n,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==e.min||s.ymax!==e.max;return Object.assign(s,n),o}const Ie=i=>i===0||i===1,Ms=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*V/e)),ks=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*V/e)+1,xe={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*Y)+1,easeOutSine:i=>Math.sin(i*Y),easeInOutSine:i=>-.5*(Math.cos(R*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>Ie(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>Ie(i)?i:Ms(i,.075,.3),easeOutElastic:i=>Ie(i)?i:ks(i,.075,.3),easeInOutElastic(i){return Ie(i)?i:i<.5?.5*Ms(i*2,.1125,.45):.5+.5*ks(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-xe.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?xe.easeInBounce(i*2)*.5:xe.easeOutBounce(i*2-1)*.5+.5};function es(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Ss(i){return es(i)?i:new ve(i)}function bi(i){return es(i)?i:new ve(i).saturate(.5).darken(.1).hexString()}const za=["x","y","borderWidth","radius","tension"],Ba=["color","borderColor","backgroundColor"];function Va(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:Ba},numbers:{type:"number",properties:za}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Wa(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const ws=new Map;function Na(i,t){t=t||{};const e=i+JSON.stringify(t);let s=ws.get(e);return s||(s=new Intl.NumberFormat(i,t),ws.set(e,s)),s}function Oe(i,t,e){return Na(t,e).format(i)}const Qn={values(i){return W(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=Ha(i,e)}const a=At(Math.abs(o)),r=isNaN(a)?1:Math.max(Math.min(-1*Math.floor(a),20),0),l={notation:n,minimumFractionDigits:r,maximumFractionDigits:r};return Object.assign(l,this.options.ticks.format),Oe(i,s,l)},logarithmic(i,t,e){if(i===0)return"0";const s=e[t].significand||i/Math.pow(10,Math.floor(At(i)));return[1,2,3,5,10,15].includes(s)||t>.8*e.length?Qn.numeric.call(this,i,t,e):""}};function Ha(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var ci={formatters:Qn};function ja(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:ci.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Ut=Object.create(null),Li=Object.create(null);function _e(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function xi(i,t,e){return typeof t=="string"?Me(_e(i,t),e):Me(_e(i,""),t)}class Ya{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>bi(n.backgroundColor),this.hoverBorderColor=(s,n)=>bi(n.borderColor),this.hoverColor=(s,n)=>bi(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return xi(this,t,e)}get(t){return _e(this,t)}describe(t,e){return xi(Li,t,e)}override(t,e){return xi(Ut,t,e)}route(t,e,s,n){const o=_e(this,t),a=_e(this,s),r="_"+e;Object.defineProperties(o,{[r]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[r],c=a[n];return L(l)?Object.assign({},c,l):D(l,c)},set(l){this[r]=l}}})}apply(t){t.forEach(e=>e(this))}}var N=new Ya({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Va,Wa,ja]);function $a(i){return!i||O(i.size)||O(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function ni(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function Ua(i,t,e,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(n=s.data={},o=s.garbageCollect=[],s.font=t),i.save(),i.font=t;let a=0;const r=e.length;let l,c,h,d,f;for(l=0;l<r;l++)if(d=e[l],d!=null&&!W(d))a=ni(i,n,o,a,d);else if(W(d))for(c=0,h=d.length;c<h;c++)f=d[c],f!=null&&!W(f)&&(a=ni(i,n,o,a,f));i.restore();const u=o.length/2;if(u>e.length){for(l=0;l<u;l++)delete n[o[l]];o.splice(0,u)}return a}function Bt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function Ps(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function Ri(i,t,e,s){Jn(i,t,e,s,null)}function Jn(i,t,e,s,n){let o,a,r,l,c,h,d,f;const u=t.pointStyle,g=t.rotation,p=t.radius;let m=(g||0)*Pa;if(u&&typeof u=="object"&&(o=u.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(m),i.drawImage(u,-u.width/2,-u.height/2,u.width,u.height),i.restore();return}if(!(isNaN(p)||p<=0)){switch(i.beginPath(),u){default:n?i.ellipse(e,s,n/2,p,0,0,V):i.arc(e,s,p,0,V),i.closePath();break;case"triangle":h=n?n/2:p,i.moveTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=xs,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=xs,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),i.closePath();break;case"rectRounded":c=p*.516,l=p-c,a=Math.cos(m+zt)*l,d=Math.cos(m+zt)*(n?n/2-c:l),r=Math.sin(m+zt)*l,f=Math.sin(m+zt)*(n?n/2-c:l),i.arc(e-d,s-r,c,m-R,m-Y),i.arc(e+f,s-a,c,m-Y,m),i.arc(e+d,s+r,c,m,m+Y),i.arc(e-f,s+a,c,m+Y,m+R),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}m+=zt;case"rectRot":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-r),i.lineTo(e+f,s-a),i.lineTo(e+d,s+r),i.lineTo(e-f,s+a),i.closePath();break;case"crossRot":m+=zt;case"cross":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+f,s-a),i.lineTo(e-f,s+a);break;case"star":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+f,s-a),i.lineTo(e-f,s+a),m+=zt,d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+f,s-a),i.lineTo(e-f,s+a);break;case"line":a=n?n/2:Math.cos(m)*p,r=Math.sin(m)*p,i.moveTo(e-a,s-r),i.lineTo(e+a,s+r);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function wt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function hi(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function di(i){i.restore()}function Xa(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function Ka(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function qa(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),O(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Ga(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),a=t-o.actualBoundingBoxLeft,r=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(a,h),i.lineTo(r,h),i.stroke()}}function Za(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Xt(i,t,e,s,n,o={}){const a=W(t)?t:[t],r=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,qa(i,o),l=0;l<a.length;++l)c=a[l],o.backdrop&&Za(i,o.backdrop),r&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),O(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),Ga(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function we(i,t){const{x:e,y:s,w:n,h:o,radius:a}=t;i.arc(e+a.topLeft,s+a.topLeft,a.topLeft,1.5*R,R,!0),i.lineTo(e,s+o-a.bottomLeft),i.arc(e+a.bottomLeft,s+o-a.bottomLeft,a.bottomLeft,R,Y,!0),i.lineTo(e+n-a.bottomRight,s+o),i.arc(e+n-a.bottomRight,s+o-a.bottomRight,a.bottomRight,Y,0,!0),i.lineTo(e+n,s+a.topRight),i.arc(e+n-a.topRight,s+a.topRight,a.topRight,0,-Y,!0),i.lineTo(e+a.topLeft,s)}const Qa=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Ja=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function tr(i,t){const e=(""+i).match(Qa);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const er=i=>+i||0;function is(i,t){const e={},s=L(t),n=s?Object.keys(t):t,o=L(i)?s?a=>D(i[a],i[t[a]]):a=>i[a]:()=>i;for(const a of n)e[a]=er(o(a));return e}function to(i){return is(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Yt(i){return is(i,["topLeft","topRight","bottomLeft","bottomRight"])}function J(i){const t=to(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function U(i,t){i=i||{},t=t||N.font;let e=D(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=D(i.style,t.style);s&&!(""+s).match(Ja)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:D(i.family,t.family),lineHeight:tr(D(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:D(i.weight,t.weight),string:""};return n.string=$a(n),n}function de(i,t,e,s){let n,o,a;for(n=0,o=i.length;n<o;++n)if(a=i[n],a!==void 0&&a!==void 0)return a}function ir(i,t,e){const{min:s,max:n}=i,o=Hn(t,(n-s)/2),a=(r,l)=>e&&r===0?0:r+l;return{min:a(s,-Math.abs(o)),max:a(n,o)}}function It(i,t){return Object.assign(Object.create(i),t)}function ss(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=no("_fallback",i));const a={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:r=>ss([r,...i],t,o,s)};return new Proxy(a,{deleteProperty(r,l){return delete r[l],delete r._keys,delete i[0][l],!0},get(r,l){return io(r,l,()=>hr(l,t,i,r))},getOwnPropertyDescriptor(r,l){return Reflect.getOwnPropertyDescriptor(r._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(r,l){return Cs(r).includes(l)},ownKeys(r){return Cs(r)},set(r,l,c){const h=r._storage||(r._storage=n());return r[l]=h[l]=c,delete r._keys,!0}})}function ie(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:eo(i,s),setContext:o=>ie(i,o,e,s),override:o=>ie(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,a){return delete o[a],delete i[a],!0},get(o,a,r){return io(o,a,()=>nr(o,a,r))},getOwnPropertyDescriptor(o,a){return o._descriptors.allKeys?Reflect.has(i,a)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,a)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,a){return Reflect.has(i,a)},ownKeys(){return Reflect.ownKeys(i)},set(o,a,r){return i[a]=r,delete o[a],!0}})}function eo(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:Et(e)?e:()=>e,isIndexable:Et(s)?s:()=>s}}const sr=(i,t)=>i?i+Zi(t):t,ns=(i,t)=>L(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function io(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function nr(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:a}=i;let r=s[t];return Et(r)&&a.isScriptable(t)&&(r=or(t,r,i,e)),W(r)&&r.length&&(r=ar(t,r,i,a.isIndexable)),ns(t,r)&&(r=ie(r,n,o&&o[t],a)),r}function or(i,t,e,s){const{_proxy:n,_context:o,_subProxy:a,_stack:r}=e;if(r.has(i))throw new Error("Recursion detected: "+Array.from(r).join("->")+"->"+i);r.add(i);let l=t(o,a||s);return r.delete(i),ns(i,l)&&(l=os(n._scopes,n,i,l)),l}function ar(i,t,e,s){const{_proxy:n,_context:o,_subProxy:a,_descriptors:r}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(L(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=os(c,n,i,h);t.push(ie(d,o,a&&a[i],r))}}return t}function so(i,t,e){return Et(i)?i(t,e):i}const rr=(i,t)=>i===!0?t:typeof i=="string"?Rt(t,i):void 0;function lr(i,t,e,s,n){for(const o of t){const a=rr(e,o);if(a){i.add(a);const r=so(a._fallback,e,n);if(typeof r<"u"&&r!==e&&r!==s)return r}else if(a===!1&&typeof s<"u"&&e!==s)return null}return!1}function os(i,t,e,s){const n=t._rootScopes,o=so(t._fallback,e,s),a=[...i,...n],r=new Set;r.add(s);let l=Ds(r,a,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=Ds(r,a,o,l,s),l===null)?!1:ss(Array.from(r),[""],n,o,()=>cr(t,e,s))}function Ds(i,t,e,s,n){for(;e;)e=lr(i,t,e,s,n);return e}function cr(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return W(n)&&L(e)?e:n||{}}function hr(i,t,e,s){let n;for(const o of t)if(n=no(sr(o,i),e),typeof n<"u")return ns(i,n)?os(e,s,i,n):n}function no(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function Cs(i){let t=i._keys;return t||(t=i._keys=dr(i._scopes)),t}function dr(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}function oo(i,t,e,s){const{iScale:n}=i,{key:o="r"}=this._parsing,a=new Array(s);let r,l,c,h;for(r=0,l=s;r<l;++r)c=r+e,h=t[c],a[r]={r:n.parse(Rt(h,o),c)};return a}const fr=Number.EPSILON||1e-14,se=(i,t)=>t<i.length&&!i[t].skip&&i[t],ao=i=>i==="x"?"y":"x";function ur(i,t,e,s){const n=i.skip?t:i,o=t,a=e.skip?t:e,r=Ti(o,n),l=Ti(a,o);let c=r/(r+l),h=l/(r+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=s*c,f=s*h;return{previous:{x:o.x-d*(a.x-n.x),y:o.y-d*(a.y-n.y)},next:{x:o.x+f*(a.x-n.x),y:o.y+f*(a.y-n.y)}}}function gr(i,t,e){const s=i.length;let n,o,a,r,l,c=se(i,0);for(let h=0;h<s-1;++h)if(l=c,c=se(i,h+1),!(!l||!c)){if(be(t[h],0,fr)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],r=Math.pow(n,2)+Math.pow(o,2),!(r<=9)&&(a=3/Math.sqrt(r),e[h]=n*a*t[h],e[h+1]=o*a*t[h])}}function pr(i,t,e="x"){const s=ao(e),n=i.length;let o,a,r,l=se(i,0);for(let c=0;c<n;++c){if(a=r,r=l,l=se(i,c+1),!r)continue;const h=r[e],d=r[s];a&&(o=(h-a[e])/3,r[`cp1${e}`]=h-o,r[`cp1${s}`]=d-o*t[c]),l&&(o=(l[e]-h)/3,r[`cp2${e}`]=h+o,r[`cp2${s}`]=d+o*t[c])}}function mr(i,t="x"){const e=ao(t),s=i.length,n=Array(s).fill(0),o=Array(s);let a,r,l,c=se(i,0);for(a=0;a<s;++a)if(r=l,l=c,c=se(i,a+1),!!l){if(c){const h=c[t]-l[t];n[a]=h!==0?(c[e]-l[e])/h:0}o[a]=r?c?mt(n[a-1])!==mt(n[a])?0:(n[a-1]+n[a])/2:n[a-1]:n[a]}gr(i,n,o),pr(i,o,t)}function Fe(i,t,e){return Math.max(Math.min(i,e),t)}function br(i,t){let e,s,n,o,a,r=wt(i[0],t);for(e=0,s=i.length;e<s;++e)a=o,o=r,r=e<s-1&&wt(i[e+1],t),o&&(n=i[e],a&&(n.cp1x=Fe(n.cp1x,t.left,t.right),n.cp1y=Fe(n.cp1y,t.top,t.bottom)),r&&(n.cp2x=Fe(n.cp2x,t.left,t.right),n.cp2y=Fe(n.cp2y,t.top,t.bottom)))}function xr(i,t,e,s,n){let o,a,r,l;if(t.spanGaps&&(i=i.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")mr(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,a=i.length;o<a;++o)r=i[o],l=ur(c,r,i[Math.min(o+1,a-(s?0:1))%a],t.tension),r.cp1x=l.previous.x,r.cp1y=l.previous.y,r.cp2x=l.next.x,r.cp2y=l.next.y,c=r}t.capBezierPoints&&br(i,e)}function as(){return typeof window<"u"&&typeof document<"u"}function rs(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function oi(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const fi=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function _r(i,t){return fi(i).getPropertyValue(t)}const yr=["top","right","bottom","left"];function $t(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=yr[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const vr=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function Mr(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let a=!1,r,l;if(vr(n,o,i.target))r=n,l=o;else{const c=t.getBoundingClientRect();r=s.clientX-c.left,l=s.clientY-c.top,a=!0}return{x:r,y:l,box:a}}function Nt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=fi(e),o=n.boxSizing==="border-box",a=$t(n,"padding"),r=$t(n,"border","width"),{x:l,y:c,box:h}=Mr(i,e),d=a.left+(h&&r.left),f=a.top+(h&&r.top);let{width:u,height:g}=t;return o&&(u-=a.width+r.width,g-=a.height+r.height),{x:Math.round((l-d)/u*e.width/s),y:Math.round((c-f)/g*e.height/s)}}function kr(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&rs(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const a=o.getBoundingClientRect(),r=fi(o),l=$t(r,"border","width"),c=$t(r,"padding");t=a.width-c.width-l.width,e=a.height-c.height-l.height,s=oi(r.maxWidth,o,"clientWidth"),n=oi(r.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||si,maxHeight:n||si}}const ze=i=>Math.round(i*10)/10;function Sr(i,t,e,s){const n=fi(i),o=$t(n,"margin"),a=oi(n.maxWidth,i,"clientWidth")||si,r=oi(n.maxHeight,i,"clientHeight")||si,l=kr(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const f=$t(n,"border","width"),u=$t(n,"padding");c-=u.width+f.width,h-=u.height+f.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=ze(Math.min(c,a,l.maxWidth)),h=ze(Math.min(h,r,l.maxHeight)),c&&!h&&(h=ze(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=ze(Math.floor(h*s))),{width:c,height:h}}function As(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const a=i.canvas;return a.style&&(e||!a.style.height&&!a.style.width)&&(a.style.height=`${i.height}px`,a.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||a.height!==n||a.width!==o?(i.currentDevicePixelRatio=s,a.height=n,a.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const wr=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};as()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function Os(i,t){const e=_r(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Ht(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function Pr(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function Dr(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},a=Ht(i,n,e),r=Ht(n,o,e),l=Ht(o,t,e),c=Ht(a,r,e),h=Ht(r,l,e);return Ht(c,h,e)}const Cr=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},Ar=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function te(i,t,e){return i?Cr(t,e):Ar()}function ro(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function lo(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function co(i){return i==="angle"?{between:Se,compare:Oa,normalize:Z}:{between:kt,compare:(t,e)=>t-e,normalize:t=>t}}function Ts({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function Or(i,t,e){const{property:s,start:n,end:o}=e,{between:a,normalize:r}=co(s),l=t.length;let{start:c,end:h,loop:d}=i,f,u;if(d){for(c+=l,h+=l,f=0,u=l;f<u&&a(r(t[c%l][s]),n,o);++f)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:i.style}}function ho(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,a=t.length,{compare:r,between:l,normalize:c}=co(s),{start:h,end:d,loop:f,style:u}=Or(i,t,e),g=[];let p=!1,m=null,b,x,y;const v=()=>l(n,y,b)&&r(n,y)!==0,_=()=>r(o,b)===0||l(o,y,b),M=()=>p||v(),S=()=>!p||_();for(let w=h,P=h;w<=d;++w)x=t[w%a],!x.skip&&(b=c(x[s]),b!==y&&(p=l(b,n,o),m===null&&M()&&(m=r(b,n)===0?w:P),m!==null&&S()&&(g.push(Ts({start:m,end:w,loop:f,count:a,style:u})),m=null),P=w,y=b));return m!==null&&g.push(Ts({start:m,end:d,loop:f,count:a,style:u})),g}function fo(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=ho(s[n],i.points,t);o.length&&e.push(...o)}return e}function Tr(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function Lr(i,t,e,s){const n=i.length,o=[];let a=t,r=i[t],l;for(l=t+1;l<=e;++l){const c=i[l%n];c.skip||c.stop?r.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=a=c.stop?l:null):(a=l,r.skip&&(t=l)),r=c}return a!==null&&o.push({start:t%n,end:a%n,loop:s}),o}function Rr(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:a,end:r}=Tr(e,n,o,s);if(s===!0)return Ls(i,[{start:a,end:r,loop:o}],e,t);const l=r<a?r+n:r,c=!!i._fullLoop&&a===0&&r===n-1;return Ls(i,Lr(e,a,l,c),e,t)}function Ls(i,t,e,s){return!s||!s.setContext||!e?t:Er(i,t,e,s)}function Er(i,t,e,s){const n=i._chart.getContext(),o=Rs(i.options),{_datasetIndex:a,options:{spanGaps:r}}=i,l=e.length,c=[];let h=o,d=t[0].start,f=d;function u(g,p,m,b){const x=r?-1:1;if(g!==p){for(g+=l;e[g%l].skip;)g-=x;for(;e[p%l].skip;)p+=x;g%l!==p%l&&(c.push({start:g%l,end:p%l,loop:m,style:b}),h=b,d=p%l)}}for(const g of t){d=r?d:g.start;let p=e[d%l],m;for(f=d+1;f<=g.end;f++){const b=e[f%l];m=Rs(s.setContext(It(n,{type:"segment",p0:p,p1:b,p0DataIndex:(f-1)%l,p1DataIndex:f%l,datasetIndex:a}))),Ir(m,h)&&u(d,f-1,g.loop,h),p=b,h=m}d<f-1&&u(d,f-1,g.loop,h)}return c}function Rs(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function Ir(i,t){if(!t)return!1;const e=[],s=function(n,o){return es(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}function Be(i,t,e){return i.options.clip?i[e]:t[e]}function Fr(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:Be(e,t,"left"),right:Be(e,t,"right"),top:Be(s,t,"top"),bottom:Be(s,t,"bottom")}:t}function uo(i,t){const e=t._clip;if(e.disabled)return!1;const s=Fr(t,i.chartArea);return{left:e.left===!1?0:s.left-(e.left===!0?0:e.left),right:e.right===!1?i.width:s.right+(e.right===!0?0:e.right),top:e.top===!1?0:s.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?i.height:s.bottom+(e.bottom===!0?0:e.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class zr{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],a=e.duration;o.forEach(r=>r({chart:t,initial:e.initial,numSteps:a,currentStep:Math.min(s-e.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=Kn.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let a=o.length-1,r=!1,l;for(;a>=0;--a)l=o[a],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),r=!0):(o[a]=o[o.length-1],o.pop());r&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var yt=new zr;const Es="transparent",Br={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=Ss(i||Es),n=s.valid&&Ss(t||Es);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class Vr{constructor(t,e,s,n){const o=e[s];n=de([t.to,n,o,t.from]);const a=de([t.from,o,n]);this._active=!0,this._fn=t.fn||Br[t.type||typeof a],this._easing=xe[t.easing]||xe.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=a,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,a=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(a,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=de([t.to,e,n,t.from]),this._from=de([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,a=this._loop,r=this._to;let l;if(this._active=o!==r&&(a||e<s),!this._active){this._target[n]=r,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=a&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,r,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class go{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!L(t))return;const e=Object.keys(N.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!L(o))return;const a={};for(const r of e)a[r]=o[r];(W(o.properties)&&o.properties||[n]).forEach(r=>{(r===n||!s.has(r))&&s.set(r,a)})})}_animateOptions(t,e){const s=e.options,n=Nr(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&Wr(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),a=Object.keys(e),r=Date.now();let l;for(l=a.length-1;l>=0;--l){const c=a[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const f=s.get(c);if(d)if(f&&d.active()){d.update(f,h,r);continue}else d.cancel();if(!f||!f.duration){t[c]=h;continue}o[c]=d=new Vr(f,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return yt.add(this._chart,s),!0}}function Wr(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function Nr(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Is(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function Hr(i,t,e){if(e===!1)return!1;const s=Is(i,e),n=Is(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function jr(i){let t,e,s,n;return L(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function po(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Fs(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let a,r,l,c;if(t===null)return;let h=!1;for(a=0,r=n.length;a<r;++a){if(l=+n[a],l===e){if(h=!0,s.all)continue;break}c=i.values[l],H(c)&&(o||t===0||mt(t)===mt(c))&&(t+=c)}return!h&&!s.all?0:t}function Yr(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",a=Object.keys(i),r=new Array(a.length);let l,c,h;for(l=0,c=a.length;l<c;++l)h=a[l],r[l]={[n]:h,[o]:i[h]};return r}function _i(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function $r(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function Ur(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function Xr(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function zs(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function Bs(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:a,index:r}=s,l=o.axis,c=a.axis,h=$r(o,a,s),d=t.length;let f;for(let u=0;u<d;++u){const g=t[u],{[l]:p,[c]:m}=g,b=g._stacks||(g._stacks={});f=b[c]=Xr(n,h,p),f[r]=m,f._top=zs(f,a,!0,s.type),f._bottom=zs(f,a,!1,s.type);const x=f._visualValues||(f._visualValues={});x[r]=m}}function yi(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function Kr(i,t){return It(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function qr(i,t,e){return It(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function oe(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const vi=i=>i==="reset"||i==="none",Vs=(i,t)=>t?i:Object.assign({},i),Gr=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:po(e,!0),values:null};class ct{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=_i(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&oe(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,f,u,g)=>d==="x"?f:d==="r"?g:u,o=e.xAxisID=D(s.xAxisID,yi(t,"x")),a=e.yAxisID=D(s.yAxisID,yi(t,"y")),r=e.rAxisID=D(s.rAxisID,yi(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,a,r),h=e.vAxisID=n(l,a,o,r);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(a),e.rScale=this.getScaleForId(r),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&vs(this._data,this),t._stacked&&oe(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(L(e)){const n=this._cachedMeta;this._data=Yr(e,n)}else if(s!==e){if(s){vs(s,this);const n=this._cachedMeta;oe(n),n._parsed=[]}e&&Object.isExtensible(e)&&Ea(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=_i(e.vScale,e),e.stack!==s.stack&&(n=!0,oe(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&(Bs(this,e._parsed),e._stacked=_i(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:a}=s,r=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,f;if(this._parsing===!1)s._parsed=n,s._sorted=!0,f=n;else{W(n[t])?f=this.parseArrayData(s,n,t,e):L(n[t])?f=this.parseObjectData(s,n,t,e):f=this.parsePrimitiveData(s,n,t,e);const u=()=>d[r]===null||c&&d[r]<c[r];for(h=0;h<e;++h)s._parsed[h+t]=d=f[h],l&&(u()&&(l=!1),c=d);s._sorted=l}a&&Bs(this,f)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:a}=t,r=o.axis,l=a.axis,c=o.getLabels(),h=o===a,d=new Array(n);let f,u,g;for(f=0,u=n;f<u;++f)g=f+s,d[f]={[r]:h||o.parse(c[g],g),[l]:a.parse(e[g],g)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:a}=t,r=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],r[l]={x:o.parse(d[0],h),y:a.parse(d[1],h)};return r}parseObjectData(t,e,s,n){const{xScale:o,yScale:a}=t,{xAxisKey:r="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,f,u;for(h=0,d=n;h<d;++h)f=h+s,u=e[f],c[h]={x:o.parse(Rt(u,r),f),y:a.parse(Rt(u,l),f)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,a=e[t.axis],r={keys:po(n,!0),values:e._stacks[t.axis]._visualValues};return Fs(r,a,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let a=o===null?NaN:o;const r=n&&s._stacks[e.axis];n&&r&&(n.values=r,a=Fs(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,a),t.max=Math.max(t.max,a)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,a=n.length,r=this._getOtherScale(t),l=Gr(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=Ur(r);let f,u;function g(){u=n[f];const p=u[r.axis];return!H(u[t.axis])||h>p||d<p}for(f=0;f<a&&!(!g()&&(this.updateRangeFromParsed(c,t,u,l),o));++f);if(o){for(f=a-1;f>=0;--f)if(!g()){this.updateRangeFromParsed(c,t,u,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,a;for(n=0,o=e.length;n<o;++n)a=e[n][t.axis],H(a)&&s.push(a);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=jr(D(this.options.clip,Hr(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,a=[],r=this._drawStart||0,l=this._drawCount||n.length-r,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,r,l),h=r;h<r+l;++h){const d=n[h];d.hidden||(d.active&&c?a.push(d):d.draw(t,o))}for(h=0;h<a.length;++h)a[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const a=this._cachedMeta.data[t];o=a.$context||(a.$context=qr(this.getContext(),t,a)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Kr(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,a=t+"-"+e,r=o[a],l=this.enableOptionSharing&&ke(s);if(r)return Vs(r,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],f=c.getOptionScopes(this.getDataset(),h),u=Object.keys(N.elements[t]),g=()=>this.getContext(s,n,e),p=c.resolveNamedOptions(f,u,g,d);return p.$shared&&(p.$shared=l,o[a]=Object.freeze(Vs(p,l))),p}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,a=`animation-${e}`,r=o[a];if(r)return r;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),f=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(f,this.getContext(t,s,e))}const c=new go(n,l&&l.animations);return l&&l._cacheable&&(o[a]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||vi(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),a=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:a}}updateElement(t,e,s,n){vi(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!vi(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[r,l,c]of this._syncList)this[r](l,c);this._syncList=[];const n=s.length,o=e.length,a=Math.min(o,n);a&&this.parse(0,a),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,a=t+e;let r;const l=c=>{for(c.length+=e,r=c.length-1;r>=a;r--)c[r]=c[r-e]};for(l(o),r=t;r<a;++r)o[r]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&oe(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}k(ct,"defaults",{}),k(ct,"datasetElementType",null),k(ct,"dataElementType",null);function Zr(i,t){if(!i._cache.$bar){const e=i.getMatchingVisibleMetas(t);let s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=Xn(s.sort((n,o)=>n-o))}return i._cache.$bar}function Qr(i){const t=i.iScale,e=Zr(t,i.type);let s=t._length,n,o,a,r;const l=()=>{a===32767||a===-32768||(ke(r)&&(s=Math.min(s,Math.abs(a-r)||s)),r=a)};for(n=0,o=e.length;n<o;++n)a=t.getPixelForValue(e[n]),l();for(r=void 0,n=0,o=t.ticks.length;n<o;++n)a=t.getPixelForTick(n),l();return s}function Jr(i,t,e,s){const n=e.barThickness;let o,a;return O(n)?(o=t.min*e.categoryPercentage,a=e.barPercentage):(o=n*s,a=1),{chunk:o/s,ratio:a,start:t.pixels[i]-o/2}}function tl(i,t,e,s){const n=t.pixels,o=n[i];let a=i>0?n[i-1]:null,r=i<n.length-1?n[i+1]:null;const l=e.categoryPercentage;a===null&&(a=o-(r===null?t.end-t.start:r-o)),r===null&&(r=o+o-a);const c=o-(o-Math.min(a,r))/2*l;return{chunk:Math.abs(r-a)/2*l/s,ratio:e.barPercentage,start:c}}function el(i,t,e,s){const n=e.parse(i[0],s),o=e.parse(i[1],s),a=Math.min(n,o),r=Math.max(n,o);let l=a,c=r;Math.abs(a)>Math.abs(r)&&(l=r,c=a),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:a,max:r}}function mo(i,t,e,s){return W(i)?el(i,t,e,s):t[e.axis]=e.parse(i,s),t}function Ws(i,t,e,s){const n=i.iScale,o=i.vScale,a=n.getLabels(),r=n===o,l=[];let c,h,d,f;for(c=e,h=e+s;c<h;++c)f=t[c],d={},d[n.axis]=r||n.parse(a[c],c),l.push(mo(f,d,o,c));return l}function Mi(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function il(i,t,e){return i!==0?mt(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function sl(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function nl(i,t,e,s){let n=t.borderSkipped;const o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:a,end:r,reverse:l,top:c,bottom:h}=sl(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=c:(e._bottom||0)===s?n=h:(o[Ns(h,a,r,l)]=!0,n=c)),o[Ns(n,a,r,l)]=!0,i.borderSkipped=o}function Ns(i,t,e,s){return s?(i=ol(i,t,e),i=Hs(i,e,t)):i=Hs(i,t,e),i}function ol(i,t,e){return i===t?e:i===e?t:i}function Hs(i,t,e){return i==="start"?t:i==="end"?e:i}function al(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}class Ue extends ct{parsePrimitiveData(t,e,s,n){return Ws(t,e,s,n)}parseArrayData(t,e,s,n){return Ws(t,e,s,n)}parseObjectData(t,e,s,n){const{iScale:o,vScale:a}=t,{xAxisKey:r="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?r:l,h=a.axis==="x"?r:l,d=[];let f,u,g,p;for(f=s,u=s+n;f<u;++f)p=e[f],g={},g[o.axis]=o.parse(Rt(p,c),f),d.push(mo(Rt(p,h),g,a,f));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);const o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),a=o._custom,r=Mi(a)?"["+a.start+", "+a.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:r}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){const o=n==="reset",{index:a,_cachedMeta:{vScale:r}}=this,l=r.getBasePixel(),c=r.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:f}=this._getSharedOptions(e,n);for(let u=e;u<e+s;u++){const g=this.getParsed(u),p=o||O(g[r.axis])?{base:l,head:l}:this._calculateBarValuePixels(u),m=this._calculateBarIndexPixels(u,h),b=(g._stacks||{})[r.axis],x={horizontal:c,base:p.base,enableBorderRadius:!b||Mi(g._custom)||a===b._top||a===b._bottom,x:c?p.head:m.center,y:c?m.center:p.head,height:c?m.size:Math.abs(p.size),width:c?Math.abs(p.size):m.size};f&&(x.options=d||this.resolveDataElementOptions(u,t[u].active?"active":n));const y=x.options||t[u].options;nl(x,y,b,a),al(x,y,h.ratio),this.updateElement(t[u],u,x,n)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=s.options.stacked,a=[],r=this._cachedMeta.controller.getParsed(e),l=r&&r[s.axis],c=h=>{const d=h._parsed.find(u=>u[s.axis]===l),f=d&&d[h.vScale.axis];if(O(f)||isNaN(f))return!0};for(const h of n)if(!(e!==void 0&&c(h))&&((o===!1||a.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&a.push(h.stack),h.index===t))break;return a.length||a.push(void 0),a}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const t=this.chart.scales,e=this.chart.options.indexAxis;return Object.keys(t).filter(s=>t[s].axis===e).shift()}_getAxis(){const t={},e=this.getFirstScaleIdForIndexAxis();for(const s of this.chart.data.datasets)t[D(this.chart.options.indexAxis==="x"?s.xAxisID:s.yAxisID,e)]=!0;return Object.keys(t)}_getStackIndex(t,e,s){const n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,s=e.iScale,n=[];let o,a;for(o=0,a=e.data.length;o<a;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const r=t.barThickness;return{min:r||Qr(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:r?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:a}}=this,r=o||0,l=this.getParsed(t),c=l._custom,h=Mi(c);let d=l[e.axis],f=0,u=s?this.applyStack(e,l,s):d,g,p;u!==d&&(f=u-d,u=d),h&&(d=c.barStart,u=c.barEnd-c.barStart,d!==0&&mt(d)!==mt(c.barEnd)&&(f=0),f+=d);const m=!O(o)&&!h?o:f;let b=e.getPixelForValue(m);if(this.chart.getDataVisibility(t)?g=e.getPixelForValue(f+u):g=b,p=g-b,Math.abs(p)<a){p=il(p,e,r)*a,d===r&&(b-=p/2);const x=e.getPixelForDecimal(0),y=e.getPixelForDecimal(1),v=Math.min(x,y),_=Math.max(x,y);b=Math.max(Math.min(b,_),v),g=b+p,s&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(g)-e.getValueForPixel(b))}if(b===e.getPixelForValue(r)){const x=mt(p)*e.getLineWidthForValue(r)/2;b+=x,p-=x}return{size:p,base:b,head:g,center:g+p/2}}_calculateBarIndexPixels(t,e){const s=e.scale,n=this.options,o=n.skipNull,a=D(n.maxBarThickness,1/0);let r,l;const c=this._getAxisCount();if(e.grouped){const h=o?this._getStackCount(t):e.stackCount,d=n.barThickness==="flex"?tl(t,e,n,h*c):Jr(t,e,n,h*c),f=this.chart.options.indexAxis==="x"?this.getDataset().xAxisID:this.getDataset().yAxisID,u=this._getAxis().indexOf(D(f,this.getFirstScaleIdForIndexAxis())),g=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0)+u;r=d.start+d.chunk*g+d.chunk/2,l=Math.min(a,d.chunk*d.ratio)}else r=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(a,e.min*e.ratio);return{base:r-l/2,head:r+l/2,center:r,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length;let o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}k(Ue,"id","bar"),k(Ue,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),k(Ue,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class Xe extends ct{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,s,n){const o=super.parsePrimitiveData(t,e,s,n);for(let a=0;a<o.length;a++)o[a]._custom=this.resolveDataElementOptions(a+s).radius;return o}parseArrayData(t,e,s,n){const o=super.parseArrayData(t,e,s,n);for(let a=0;a<o.length;a++){const r=e[s+a];o[a]._custom=D(r[2],this.resolveDataElementOptions(a+s).radius)}return o}parseObjectData(t,e,s,n){const o=super.parseObjectData(t,e,s,n);for(let a=0;a<o.length;a++){const r=e[s+a];o[a]._custom=D(r&&r.r&&+r.r,this.resolveDataElementOptions(a+s).radius)}return o}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let s=t.length-1;s>=0;--s)e=Math.max(e,t[s].size(this.resolveDataElementOptions(s))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart.data.labels||[],{xScale:n,yScale:o}=e,a=this.getParsed(t),r=n.getLabelForValue(a.x),l=o.getLabelForValue(a.y),c=a._custom;return{label:s[t]||"",value:"("+r+", "+l+(c?", "+c:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:a,vScale:r}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,n),h=a.axis,d=r.axis;for(let f=e;f<e+s;f++){const u=t[f],g=!o&&this.getParsed(f),p={},m=p[h]=o?a.getPixelForDecimal(.5):a.getPixelForValue(g[h]),b=p[d]=o?r.getBasePixel():r.getPixelForValue(g[d]);p.skip=isNaN(m)||isNaN(b),c&&(p.options=l||this.resolveDataElementOptions(f,u.active?"active":n),o&&(p.options.radius=0)),this.updateElement(u,f,p,n)}}resolveDataElementOptions(t,e){const s=this.getParsed(t);let n=super.resolveDataElementOptions(t,e);n.$shared&&(n=Object.assign({},n,{$shared:!1}));const o=n.radius;return e!=="active"&&(n.radius=0),n.radius+=D(s&&s._custom,o),n}}k(Xe,"id","bubble"),k(Xe,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),k(Xe,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function rl(i,t,e){let s=1,n=1,o=0,a=0;if(t<V){const r=i,l=r+t,c=Math.cos(r),h=Math.sin(r),d=Math.cos(l),f=Math.sin(l),u=(y,v,_)=>Se(y,r,l,!0)?1:Math.max(v,v*e,_,_*e),g=(y,v,_)=>Se(y,r,l,!0)?-1:Math.min(v,v*e,_,_*e),p=u(0,c,d),m=u(Y,h,f),b=g(R,c,d),x=g(R+Y,h,f);s=(p-b)/2,n=(m-x)/2,o=-(p+b)/2,a=-(m+x)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:a}}class jt extends ct{constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const s=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=s;else{let o=l=>+s[l];if(L(s[t])){const{key:l="value"}=this._parsing;o=c=>+Rt(s[c],l)}let a,r;for(a=t,r=t+e;a<r;++a)n._parsed[a]=o(a)}}_getRotation(){return lt(this.options.rotation-90)}_getCircumference(){return lt(this.options.circumference)}_getRotationExtents(){let t=V,e=-V;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const n=this.chart.getDatasetMeta(s).controller,o=n._getRotation(),a=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+a)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:s}=e,n=this._cachedMeta,o=n.data,a=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,r=Math.max((Math.min(s.width,s.height)-a)/2,0),l=Math.min(_a(this.options.cutout,r),1),c=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:f,ratioY:u,offsetX:g,offsetY:p}=rl(d,h,l),m=(s.width-a)/f,b=(s.height-a)/u,x=Math.max(Math.min(m,b)/2,0),y=Hn(this.options.radius,x),v=Math.max(y*l,0),_=(y-v)/this._getVisibleDatasetWeightTotal();this.offsetX=g*y,this.offsetY=p*y,n.total=this.calculateTotal(),this.outerRadius=y-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){const s=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/V)}updateElements(t,e,s,n){const o=n==="reset",a=this.chart,r=a.chartArea,c=a.options.animation,h=(r.left+r.right)/2,d=(r.top+r.bottom)/2,f=o&&c.animateScale,u=f?0:this.innerRadius,g=f?0:this.outerRadius,{sharedOptions:p,includeOptions:m}=this._getSharedOptions(e,n);let b=this._getRotation(),x;for(x=0;x<e;++x)b+=this._circumference(x,o);for(x=e;x<e+s;++x){const y=this._circumference(x,o),v=t[x],_={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+y,circumference:y,outerRadius:g,innerRadius:u};m&&(_.options=p||this.resolveDataElementOptions(x,v.active?"active":n)),b+=y,this.updateElement(v,x,_,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let s=0,n;for(n=0;n<e.length;n++){const o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?V*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=Oe(e._parsed[t],s.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0;const s=this.chart;let n,o,a,r,l;if(!t){for(n=0,o=s.data.datasets.length;n<o;++n)if(s.isDatasetVisible(n)){a=s.getDatasetMeta(n),t=a.data,r=a.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)l=r.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let s=0,n=t.length;s<n;++s){const o=this.resolveDataElementOptions(s);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(D(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}k(jt,"id","doughnut"),k(jt,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),k(jt,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),k(jt,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,a)=>{const l=t.getDatasetMeta(0).controller.getStyle(a);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}}});class Ke extends ct{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:s,data:n=[],_dataset:o}=e,a=this.chart._animationsDisabled;let{start:r,count:l}=Gn(e,n,a);this._drawStart=r,this._drawCount=l,Zn(e)&&(r=0,l=n.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=n;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!a,options:c},t),this.updateElements(n,r,l,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:a,vScale:r,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,n),f=a.axis,u=r.axis,{spanGaps:g,segment:p}=this.options,m=ee(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||n==="none",x=e+s,y=t.length;let v=e>0&&this.getParsed(e-1);for(let _=0;_<y;++_){const M=t[_],S=b?M:{};if(_<e||_>=x){S.skip=!0;continue}const w=this.getParsed(_),P=O(w[u]),C=S[f]=a.getPixelForValue(w[f],_),A=S[u]=o||P?r.getBasePixel():r.getPixelForValue(l?this.applyStack(r,w,l):w[u],_);S.skip=isNaN(C)||isNaN(A)||P,S.stop=_>0&&Math.abs(w[f]-v[f])>m,p&&(S.parsed=w,S.raw=c.data[_]),d&&(S.options=h||this.resolveDataElementOptions(_,M.active?"active":n)),b||this.updateElement(M,_,S,n),v=w}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,s=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return s;const o=n[0].size(this.resolveDataElementOptions(0)),a=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(s,o,a)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}k(Ke,"id","line"),k(Ke,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),k(Ke,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class ye extends ct{constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=Oe(e._parsed[t].r,s.options.locale);return{label:n[t]||"",value:o}}parseObjectData(t,e,s,n){return oo.bind(this)(t,e,s,n)}update(t){const e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){const t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((s,n)=>{const o=this.getParsed(n).r;!isNaN(o)&&this.chart.getDataVisibility(n)&&(o<e.min&&(e.min=o),o>e.max&&(e.max=o))}),e}_updateRadius(){const t=this.chart,e=t.chartArea,s=t.options,n=Math.min(e.right-e.left,e.bottom-e.top),o=Math.max(n/2,0),a=Math.max(s.cutoutPercentage?o/100*s.cutoutPercentage:1,0),r=(o-a)/t.getVisibleDatasetCount();this.outerRadius=o-r*this.index,this.innerRadius=this.outerRadius-r}updateElements(t,e,s,n){const o=n==="reset",a=this.chart,l=a.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,d=c.yCenter,f=c.getIndexAngle(0)-.5*R;let u=f,g;const p=360/this.countVisibleElements();for(g=0;g<e;++g)u+=this._computeAngle(g,n,p);for(g=e;g<e+s;g++){const m=t[g];let b=u,x=u+this._computeAngle(g,n,p),y=a.getDataVisibility(g)?c.getDistanceFromCenterForValue(this.getParsed(g).r):0;u=x,o&&(l.animateScale&&(y=0),l.animateRotate&&(b=x=f));const v={x:h,y:d,innerRadius:0,outerRadius:y,startAngle:b,endAngle:x,options:this.resolveDataElementOptions(g,m.active?"active":n)};this.updateElement(m,g,v,n)}}countVisibleElements(){const t=this._cachedMeta;let e=0;return t.data.forEach((s,n)=>{!isNaN(this.getParsed(n).r)&&this.chart.getDataVisibility(n)&&e++}),e}_computeAngle(t,e,s){return this.chart.getDataVisibility(t)?lt(this.resolveDataElementOptions(t,e).angle||s):0}}k(ye,"id","polarArea"),k(ye,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),k(ye,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,a)=>{const l=t.getDatasetMeta(0).controller.getStyle(a);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class Ei extends jt{}k(Ei,"id","pie"),k(Ei,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class qe extends ct{getLabelAndValue(t){const e=this._cachedMeta.vScale,s=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(s[e.axis])}}parseObjectData(t,e,s,n){return oo.bind(this)(t,e,s,n)}update(t){const e=this._cachedMeta,s=e.dataset,n=e.data||[],o=e.iScale.getLabels();if(s.points=n,t!=="resize"){const a=this.resolveDatasetElementOptions(t);this.options.showLine||(a.borderWidth=0);const r={_loop:!0,_fullLoop:o.length===n.length,options:a};this.updateElement(s,void 0,r,t)}this.updateElements(n,0,n.length,t)}updateElements(t,e,s,n){const o=this._cachedMeta.rScale,a=n==="reset";for(let r=e;r<e+s;r++){const l=t[r],c=this.resolveDataElementOptions(r,l.active?"active":n),h=o.getPointPositionForValue(r,this.getParsed(r).r),d=a?o.xCenter:h.x,f=a?o.yCenter:h.y,u={x:d,y:f,angle:h.angle,skip:isNaN(d)||isNaN(f),options:c};this.updateElement(l,r,u,n)}}}k(qe,"id","radar"),k(qe,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),k(qe,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class Ge extends ct{getLabelAndValue(t){const e=this._cachedMeta,s=this.chart.data.labels||[],{xScale:n,yScale:o}=e,a=this.getParsed(t),r=n.getLabelForValue(a.x),l=o.getLabelForValue(a.y);return{label:s[t]||"",value:"("+r+", "+l+")"}}update(t){const e=this._cachedMeta,{data:s=[]}=e,n=this.chart._animationsDisabled;let{start:o,count:a}=Gn(e,s,n);if(this._drawStart=o,this._drawCount=a,Zn(e)&&(o=0,a=s.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:r,_dataset:l}=e;r._chart=this.chart,r._datasetIndex=this.index,r._decimated=!!l._decimated,r.points=s;const c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(r,void 0,{animated:!n,options:c},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(s,o,a,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,s,n){const o=n==="reset",{iScale:a,vScale:r,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(e,n),d=this.getSharedOptions(h),f=this.includeOptions(n,d),u=a.axis,g=r.axis,{spanGaps:p,segment:m}=this.options,b=ee(p)?p:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||o||n==="none";let y=e>0&&this.getParsed(e-1);for(let v=e;v<e+s;++v){const _=t[v],M=this.getParsed(v),S=x?_:{},w=O(M[g]),P=S[u]=a.getPixelForValue(M[u],v),C=S[g]=o||w?r.getBasePixel():r.getPixelForValue(l?this.applyStack(r,M,l):M[g],v);S.skip=isNaN(P)||isNaN(C)||w,S.stop=v>0&&Math.abs(M[u]-y[u])>b,m&&(S.parsed=M,S.raw=c.data[v]),f&&(S.options=d||this.resolveDataElementOptions(v,_.active?"active":n)),x||this.updateElement(_,v,S,n),y=M}this.updateSharedOptions(d,n,h)}getMaxOverflow(){const t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let r=0;for(let l=e.length-1;l>=0;--l)r=Math.max(r,e[l].size(this.resolveDataElementOptions(l))/2);return r>0&&r}const s=t.dataset,n=s.options&&s.options.borderWidth||0;if(!e.length)return n;const o=e[0].size(this.resolveDataElementOptions(0)),a=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(n,o,a)/2}}k(Ge,"id","scatter"),k(Ge,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),k(Ge,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var ll=Object.freeze({__proto__:null,BarController:Ue,BubbleController:Xe,DoughnutController:jt,LineController:Ke,PieController:Ei,PolarAreaController:ye,RadarController:qe,ScatterController:Ge});function Vt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class ls{constructor(t){k(this,"options");this.options=t||{}}static override(t){Object.assign(ls.prototype,t)}init(){}formats(){return Vt()}parse(){return Vt()}format(){return Vt()}add(){return Vt()}diff(){return Vt()}startOf(){return Vt()}endOf(){return Vt()}}var cl={_date:ls};function hl(i,t,e,s){const{controller:n,data:o,_sorted:a}=i,r=n._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(r&&t===r.axis&&t!=="r"&&a&&o.length){const c=r._reversePixels?La:St;if(s){if(n._sharedOptions){const h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const f=c(o,t,e-d),u=c(o,t,e+d);return{lo:f.lo,hi:u.hi}}}}else{const h=c(o,t,e);if(l){const{vScale:d}=n._cachedMeta,{_parsed:f}=i,u=f.slice(0,h.lo+1).reverse().findIndex(p=>!O(p[d.axis]));h.lo-=Math.max(0,u);const g=f.slice(h.hi).findIndex(p=>!O(p[d.axis]));h.hi+=Math.max(0,g)}return h}}return{lo:0,hi:o.length-1}}function ui(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),a=e[t];for(let r=0,l=o.length;r<l;++r){const{index:c,data:h}=o[r],{lo:d,hi:f}=hl(o[r],t,a,n);for(let u=d;u<=f;++u){const g=h[u];g.skip||s(g,c,u)}}}function dl(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,a=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(a,2))}}function ki(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||ui(i,e,t,function(r,l,c){!n&&!wt(r,i.chartArea,0)||r.inRange(t.x,t.y,s)&&o.push({element:r,datasetIndex:l,index:c})},!0),o}function fl(i,t,e,s){let n=[];function o(a,r,l){const{startAngle:c,endAngle:h}=a.getProps(["startAngle","endAngle"],s),{angle:d}=$n(a,{x:t.x,y:t.y});Se(d,c,h)&&n.push({element:a,datasetIndex:r,index:l})}return ui(i,e,t,o),n}function ul(i,t,e,s,n,o){let a=[];const r=dl(e);let l=Number.POSITIVE_INFINITY;function c(h,d,f){const u=h.inRange(t.x,t.y,n);if(s&&!u)return;const g=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(g))&&!u)return;const m=r(t,g);m<l?(a=[{element:h,datasetIndex:d,index:f}],l=m):m===l&&a.push({element:h,datasetIndex:d,index:f})}return ui(i,e,t,c),a}function Si(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?fl(i,t,e,n):ul(i,t,e,s,n,o)}function js(i,t,e,s,n){const o=[],a=e==="x"?"inXRange":"inYRange";let r=!1;return ui(i,e,t,(l,c,h)=>{l[a]&&l[a](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),r=r||l.inRange(t.x,t.y,n))}),s&&!r?[]:o}var gl={modes:{index(i,t,e,s){const n=Nt(t,i),o=e.axis||"x",a=e.includeInvisible||!1,r=e.intersect?ki(i,n,o,s,a):Si(i,n,o,!1,s,a),l=[];return r.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=r[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=Nt(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;let r=e.intersect?ki(i,n,o,s,a):Si(i,n,o,!1,s,a);if(r.length>0){const l=r[0].datasetIndex,c=i.getDatasetMeta(l).data;r=[];for(let h=0;h<c.length;++h)r.push({element:c[h],datasetIndex:l,index:h})}return r},point(i,t,e,s){const n=Nt(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;return ki(i,n,o,s,a)},nearest(i,t,e,s){const n=Nt(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;return Si(i,n,o,e.intersect,s,a)},x(i,t,e,s){const n=Nt(t,i);return js(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=Nt(t,i);return js(i,n,"y",e.intersect,s)}}};const bo=["left","top","right","bottom"];function ae(i,t){return i.filter(e=>e.pos===t)}function Ys(i,t){return i.filter(e=>bo.indexOf(e.pos)===-1&&e.box.axis===t)}function re(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function pl(i){const t=[];let e,s,n,o,a,r;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:a,stackWeight:r=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:a&&o+a,stackWeight:r});return t}function ml(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!bo.includes(n))continue;const a=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=o}return t}function bl(i,t){const e=ml(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,a,r;for(o=0,a=i.length;o<a;++o){r=i[o];const{fullSize:l}=r.box,c=e[r.stack],h=c&&r.stackWeight/c.weight;r.horizontal?(r.width=h?h*s:l&&t.availableWidth,r.height=n):(r.width=s,r.height=h?h*n:l&&t.availableHeight)}return e}function xl(i){const t=pl(i),e=re(t.filter(c=>c.box.fullSize),!0),s=re(ae(t,"left"),!0),n=re(ae(t,"right")),o=re(ae(t,"top"),!0),a=re(ae(t,"bottom")),r=Ys(t,"x"),l=Ys(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(a).concat(r),chartArea:ae(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(a).concat(r)}}function $s(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function xo(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function _l(i,t,e,s){const{pos:n,box:o}=e,a=i.maxPadding;if(!L(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&xo(a,o.getPadding());const r=Math.max(0,t.outerWidth-$s(a,i,"left","right")),l=Math.max(0,t.outerHeight-$s(a,i,"top","bottom")),c=r!==i.w,h=l!==i.h;return i.w=r,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function yl(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function vl(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(a=>{o[a]=Math.max(t[a],e[a])}),o}return s(i?["left","right"]:["top","bottom"])}function fe(i,t,e,s){const n=[];let o,a,r,l,c,h;for(o=0,a=i.length,c=0;o<a;++o){r=i[o],l=r.box,l.update(r.width||t.w,r.height||t.h,vl(r.horizontal,t));const{same:d,other:f}=_l(t,e,r,s);c|=d&&n.length,h=h||f,l.fullSize||n.push(r)}return c&&fe(n,t,e,s)||h}function Ve(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function Us(i,t,e,s){const n=e.padding;let{x:o,y:a}=t;for(const r of i){const l=r.box,c=s[r.stack]||{placed:0,weight:1},h=r.stackWeight/c.weight||1;if(r.horizontal){const d=t.w*h,f=c.size||l.height;ke(c.start)&&(a=c.start),l.fullSize?Ve(l,n.left,a,e.outerWidth-n.right-n.left,f):Ve(l,t.left+c.placed,a,d,f),c.start=a,c.placed+=d,a=l.bottom}else{const d=t.h*h,f=c.size||l.width;ke(c.start)&&(o=c.start),l.fullSize?Ve(l,o,n.top,f,e.outerHeight-n.bottom-n.top):Ve(l,o,t.top+c.placed,f,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=a}var Q={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=J(i.options.layout.padding),o=Math.max(t-n.width,0),a=Math.max(e-n.height,0),r=xl(i.boxes),l=r.vertical,c=r.horizontal;I(i.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});const h=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:a,vBoxMaxWidth:o/2/h,hBoxMaxHeight:a/2}),f=Object.assign({},n);xo(f,J(s));const u=Object.assign({maxPadding:f,w:o,h:a,x:n.left,y:n.top},n),g=bl(l.concat(c),d);fe(r.fullSize,u,d,g),fe(l,u,d,g),fe(c,u,d,g)&&fe(l,u,d,g),yl(u),Us(r.leftAndTop,u,d,g),u.x+=u.w,u.y+=u.h,Us(r.rightAndBottom,u,d,g),i.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},I(r.chartArea,p=>{const m=p.box;Object.assign(m,i.chartArea),m.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class _o{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class Ml extends _o{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const Ze="$chartjs",kl={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Xs=i=>i===null||i==="";function Sl(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[Ze]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Xs(n)){const o=Os(i,"width");o!==void 0&&(i.width=o)}if(Xs(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=Os(i,"height");o!==void 0&&(i.height=o)}return i}const yo=wr?{passive:!0}:!1;function wl(i,t,e){i&&i.addEventListener(t,e,yo)}function Pl(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,yo)}function Dl(i,t){const e=kl[i.type]||i.type,{x:s,y:n}=Nt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function ai(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function Cl(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let a=!1;for(const r of o)a=a||ai(r.addedNodes,s),a=a&&!ai(r.removedNodes,s);a&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function Al(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let a=!1;for(const r of o)a=a||ai(r.removedNodes,s),a=a&&!ai(r.addedNodes,s);a&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const Pe=new Map;let Ks=0;function vo(){const i=window.devicePixelRatio;i!==Ks&&(Ks=i,Pe.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function Ol(i,t){Pe.size||window.addEventListener("resize",vo),Pe.set(i,t)}function Tl(i){Pe.delete(i),Pe.size||window.removeEventListener("resize",vo)}function Ll(i,t,e){const s=i.canvas,n=s&&rs(s);if(!n)return;const o=qn((r,l)=>{const c=n.clientWidth;e(r,l),c<n.clientWidth&&e()},window),a=new ResizeObserver(r=>{const l=r[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return a.observe(n),Ol(i,o),a}function wi(i,t,e){e&&e.disconnect(),t==="resize"&&Tl(i)}function Rl(i,t,e){const s=i.canvas,n=qn(o=>{i.ctx!==null&&e(Dl(o,i))},i);return wl(s,t,n),n}class El extends _o{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Sl(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[Ze])return!1;const s=e[Ze].initial;["height","width"].forEach(o=>{const a=s[o];O(a)?e.removeAttribute(o):e.setAttribute(o,a)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[Ze],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),a={attach:Cl,detach:Al,resize:Ll}[e]||Rl;n[e]=a(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:wi,detach:wi,resize:wi}[e]||Pl)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return Sr(t,e,s,n)}isAttached(t){const e=t&&rs(t);return!!(e&&e.isConnected)}}function Il(i){return!as()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Ml:El}class ht{constructor(){k(this,"x");k(this,"y");k(this,"active",!1);k(this,"options");k(this,"$animations")}tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return ee(this.x)&&ee(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}k(ht,"defaults",{}),k(ht,"defaultRoutes");function Fl(i,t){const e=i.options.ticks,s=zl(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?Vl(t):[],a=o.length,r=o[0],l=o[a-1],c=[];if(a>n)return Wl(t,c,o,a/n),c;const h=Bl(o,t,n);if(a>0){let d,f;const u=a>1?Math.round((l-r)/(a-1)):null;for(We(t,c,h,O(u)?0:r-u,r),d=0,f=a-1;d<f;d++)We(t,c,h,o[d],o[d+1]);return We(t,c,h,l,O(u)?t.length:l+u),c}return We(t,c,h),c}function zl(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function Bl(i,t,e){const s=Nl(i),n=t.length/e;if(!s)return Math.max(n,1);const o=Da(s);for(let a=0,r=o.length-1;a<r;a++){const l=o[a];if(l>n)return l}return Math.max(n,1)}function Vl(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function Wl(i,t,e,s){let n=0,o=e[0],a;for(s=Math.ceil(s),a=0;a<i.length;a++)a===o&&(t.push(i[a]),n++,o=e[n*s])}function We(i,t,e,s,n){const o=D(s,0),a=Math.min(D(n,i.length),i.length);let r=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)r++,h=Math.round(o+r*e);for(c=Math.max(o,0);c<a;c++)c===h&&(t.push(i[c]),r++,h=Math.round(o+r*e))}function Nl(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const Hl=i=>i==="left"?"right":i==="right"?"left":i,qs=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,Gs=(i,t)=>Math.min(t||i,i);function Zs(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function jl(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,a=i._endPixel,r=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,a-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-r||l>a+r)))return l}function Yl(i,t){I(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function le(i){return i.drawTicks?i.tickLength:0}function Qs(i,t){if(!i.display)return 0;const e=U(i.font,t),s=J(i.padding);return(W(i.text)?i.text.length:1)*e.lineHeight+s.height}function $l(i,t){return It(i,{scale:t,type:"scale"})}function Ul(i,t,e){return It(i,{tick:e,index:t,type:"tick"})}function Xl(i,t,e){let s=ts(i);return(e&&t!=="right"||!e&&t==="right")&&(s=Hl(s)),s}function Kl(i,t,e,s){const{top:n,left:o,bottom:a,right:r,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,f,u,g;const p=a-n,m=r-o;if(i.isHorizontal()){if(u=G(s,o,r),L(e)){const b=Object.keys(e)[0],x=e[b];g=h[b].getPixelForValue(x)+p-t}else e==="center"?g=(c.bottom+c.top)/2+p-t:g=qs(i,e,t);f=r-o}else{if(L(e)){const b=Object.keys(e)[0],x=e[b];u=h[b].getPixelForValue(x)-m+t}else e==="center"?u=(c.left+c.right)/2-m+t:u=qs(i,e,t);g=G(s,a,n),d=e==="left"?-Y:Y}return{titleX:u,titleY:g,maxWidth:f,rotation:d}}class Kt extends ht{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=ot(t,Number.POSITIVE_INFINITY),e=ot(e,Number.NEGATIVE_INFINITY),s=ot(s,Number.POSITIVE_INFINITY),n=ot(n,Number.NEGATIVE_INFINITY),{min:ot(t,s),max:ot(e,n),minDefined:H(t),maxDefined:H(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),a;if(n&&o)return{min:e,max:s};const r=this.getMatchingVisibleMetas();for(let l=0,c=r.length;l<c;++l)a=r[l].controller.getMinMax(this,t),n||(e=Math.min(e,a.min)),o||(s=Math.max(s,a.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:ot(e,ot(s,e)),max:ot(s,ot(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){z(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:a}=this.options,r=a.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=ir(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=r<this.ticks.length;this._convertTicksToLabels(l?Zs(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||a.source==="auto")&&(this.ticks=Fl(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){z(this.options.afterUpdate,[this])}beforeSetDimensions(){z(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){z(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),z(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){z(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=z(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){z(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){z(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=Gs(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let a=n,r,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,f=h.highest.height,u=K(this.chart.width-d,0,this.maxWidth);r=t.offset?this.maxWidth/s:u/(s-1),d+6>r&&(r=u/(s-(t.offset?.5:1)),l=this.maxHeight-le(t.grid)-e.padding-Qs(t.title,this.chart.options.font),c=Math.sqrt(d*d+f*f),a=Qi(Math.min(Math.asin(K((h.highest.height+6)/r,-1,1)),Math.asin(K(l/c,-1,1))-Math.asin(K(f/c,-1,1)))),a=Math.max(n,Math.min(o,a))),this.labelRotation=a}afterCalculateLabelRotation(){z(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){z(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,a=this._isVisible(),r=this.isHorizontal();if(a){const l=Qs(n,e.options.font);if(r?(t.width=this.maxWidth,t.height=le(o)+l):(t.height=this.maxHeight,t.width=le(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:f}=this._getLabelSizes(),u=s.padding*2,g=lt(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(r){const b=s.mirror?0:m*d.width+p*f.height;t.height=Math.min(this.maxHeight,t.height+b+u)}else{const b=s.mirror?0:p*d.width+m*f.height;t.width=Math.min(this.maxWidth,t.width+b+u)}this._calculatePadding(c,h,m,p)}}this._handleMargins(),r?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:a},position:r}=this.options,l=this.labelRotation!==0,c=r!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,u=0;l?c?(f=n*t.width,u=s*e.height):(f=s*t.height,u=n*e.width):o==="start"?u=e.width:o==="end"?f=t.width:o!=="inner"&&(f=t.width/2,u=e.width/2),this.paddingLeft=Math.max((f-h+a)*this.width/(this.width-h),0),this.paddingRight=Math.max((u-d+a)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+a,this.paddingBottom=d+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){z(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)O(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=Zs(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,a=[],r=[],l=Math.floor(e/Gs(e,s));let c=0,h=0,d,f,u,g,p,m,b,x,y,v,_;for(d=0;d<e;d+=l){if(g=t[d].label,p=this._resolveTickFontOptions(d),n.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},x=p.lineHeight,y=v=0,!O(g)&&!W(g))y=ni(n,b.data,b.gc,y,g),v=x;else if(W(g))for(f=0,u=g.length;f<u;++f)_=g[f],!O(_)&&!W(_)&&(y=ni(n,b.data,b.gc,y,_),v+=x);a.push(y),r.push(v),c=Math.max(y,c),h=Math.max(v,h)}Yl(o,e);const M=a.indexOf(c),S=r.indexOf(h),w=P=>({width:a[P]||0,height:r[P]||0});return{first:w(0),last:w(e-1),widest:w(M),highest:w(S),widths:a,heights:r}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return Ta(this._alignToPixels?Bt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=Ul(this.getContext(),t,s))}return this.$context||(this.$context=$l(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=lt(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),a=t.autoSkipPadding||0,r=o?o.widest.width+a:0,l=o?o.highest.height+a:0;return this.isHorizontal()?l*s>r*n?r/s:l/n:l*n<r*s?l/s:r/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:a,border:r}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),f=le(o),u=[],g=r.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(F){return Bt(s,F,p)};let x,y,v,_,M,S,w,P,C,A,T,$;if(a==="top")x=b(this.bottom),S=this.bottom-f,P=x-m,A=b(t.top)+m,$=t.bottom;else if(a==="bottom")x=b(this.top),A=t.top,$=b(t.bottom)-m,S=x+m,P=this.top+f;else if(a==="left")x=b(this.right),M=this.right-f,w=x-m,C=b(t.left)+m,T=t.right;else if(a==="right")x=b(this.left),C=t.left,T=b(t.right)-m,M=x+m,w=this.left+f;else if(e==="x"){if(a==="center")x=b((t.top+t.bottom)/2+.5);else if(L(a)){const F=Object.keys(a)[0],B=a[F];x=b(this.chart.scales[F].getPixelForValue(B))}A=t.top,$=t.bottom,S=x+m,P=S+f}else if(e==="y"){if(a==="center")x=b((t.left+t.right)/2);else if(L(a)){const F=Object.keys(a)[0],B=a[F];x=b(this.chart.scales[F].getPixelForValue(B))}M=x-m,w=M-f,C=t.left,T=t.right}const q=D(n.ticks.maxTicksLimit,d),E=Math.max(1,Math.ceil(d/q));for(y=0;y<d;y+=E){const F=this.getContext(y),B=o.setContext(F),it=r.setContext(F),X=B.lineWidth,dt=B.color,qt=it.dash||[],Pt=it.dashOffset,Dt=B.tickWidth,bt=B.tickColor,Ft=B.tickBorderDash||[],j=B.tickBorderDashOffset;v=jl(this,y,l),v!==void 0&&(_=Bt(s,v,X),c?M=w=C=T=_:S=P=A=$=_,u.push({tx1:M,ty1:S,tx2:w,ty2:P,x1:C,y1:A,x2:T,y2:$,width:X,color:dt,borderDash:qt,borderDashOffset:Pt,tickWidth:Dt,tickColor:bt,tickBorderDash:Ft,tickBorderDashOffset:j}))}return this._ticksLength=d,this._borderValue=x,u}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,a=this.isHorizontal(),r=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,f=le(s.grid),u=f+h,g=d?-h:u,p=-lt(this.labelRotation),m=[];let b,x,y,v,_,M,S,w,P,C,A,T,$="middle";if(n==="top")M=this.bottom-g,S=this._getXAxisLabelAlignment();else if(n==="bottom")M=this.top+g,S=this._getXAxisLabelAlignment();else if(n==="left"){const E=this._getYAxisLabelAlignment(f);S=E.textAlign,_=E.x}else if(n==="right"){const E=this._getYAxisLabelAlignment(f);S=E.textAlign,_=E.x}else if(e==="x"){if(n==="center")M=(t.top+t.bottom)/2+u;else if(L(n)){const E=Object.keys(n)[0],F=n[E];M=this.chart.scales[E].getPixelForValue(F)+u}S=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")_=(t.left+t.right)/2-u;else if(L(n)){const E=Object.keys(n)[0],F=n[E];_=this.chart.scales[E].getPixelForValue(F)}S=this._getYAxisLabelAlignment(f).textAlign}e==="y"&&(l==="start"?$="top":l==="end"&&($="bottom"));const q=this._getLabelSizes();for(b=0,x=r.length;b<x;++b){y=r[b],v=y.label;const E=o.setContext(this.getContext(b));w=this.getPixelForTick(b)+o.labelOffset,P=this._resolveTickFontOptions(b),C=P.lineHeight,A=W(v)?v.length:1;const F=A/2,B=E.color,it=E.textStrokeColor,X=E.textStrokeWidth;let dt=S;a?(_=w,S==="inner"&&(b===x-1?dt=this.options.reverse?"left":"right":b===0?dt=this.options.reverse?"right":"left":dt="center"),n==="top"?c==="near"||p!==0?T=-A*C+C/2:c==="center"?T=-q.highest.height/2-F*C+C:T=-q.highest.height+C/2:c==="near"||p!==0?T=C/2:c==="center"?T=q.highest.height/2-F*C:T=q.highest.height-A*C,d&&(T*=-1),p!==0&&!E.showLabelBackdrop&&(_+=C/2*Math.sin(p))):(M=w,T=(1-A)*C/2);let qt;if(E.showLabelBackdrop){const Pt=J(E.backdropPadding),Dt=q.heights[b],bt=q.widths[b];let Ft=T-Pt.top,j=0-Pt.left;switch($){case"middle":Ft-=Dt/2;break;case"bottom":Ft-=Dt;break}switch(S){case"center":j-=bt/2;break;case"right":j-=bt;break;case"inner":b===x-1?j-=bt:b>0&&(j-=bt/2);break}qt={left:j,top:Ft,width:bt+Pt.width,height:Dt+Pt.height,color:E.backdropColor}}m.push({label:v,font:P,textOffset:T,options:{rotation:p,color:B,strokeColor:it,strokeWidth:X,textAlign:dt,textBaseline:$,translation:[_,M],backdrop:qt}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-lt(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,a=this._getLabelSizes(),r=t+o,l=a.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-r,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+r,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:a}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,a),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,a;const r=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,a=n.length;o<a;++o){const l=n[o];e.drawOnChartArea&&r({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&r({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),a=s.display?o.width:0;if(!a)return;const r=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,f;this.isHorizontal()?(c=Bt(t,this.left,a)-a/2,h=Bt(t,this.right,r)+r/2,d=f=l):(d=Bt(t,this.top,a)-a/2,f=Bt(t,this.bottom,r)+r/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,f),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&hi(s,n);const o=this.getLabelItems(t);for(const a of o){const r=a.options,l=a.font,c=a.label,h=a.textOffset;Xt(s,c,0,h,l,r)}n&&di(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=U(s.font),a=J(s.padding),r=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||L(e)?(l+=a.bottom,W(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=a.top;const{titleX:c,titleY:h,maxWidth:d,rotation:f}=Kl(this,l,e,r);Xt(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:f,textAlign:Xl(r,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=D(t.grid&&t.grid.z,-1),n=D(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Kt.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,a;for(o=0,a=e.length;o<a;++o){const r=e[o];r[s]===this.id&&(!t||r.type===t)&&n.push(r)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return U(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Ne{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;Zl(e)&&(s=this.register(e));const n=this.items,o=t.id,a=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,ql(t,a,s),this.override&&N.override(t.id,t.overrides)),a}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in N[n]&&(delete N[n][s],this.override&&delete Ut[s])}}function ql(i,t,e){const s=Me(Object.create(null),[e?N.get(e):{},N.get(t),i.defaults]);N.set(t,s),i.defaultRoutes&&Gl(t,i.defaultRoutes),i.descriptors&&N.describe(t,i.descriptors)}function Gl(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),a=t[e].split("."),r=a.pop(),l=a.join(".");N.route(o,n,l,r)})}function Zl(i){return"id"in i&&"defaults"in i}class Ql{constructor(){this.controllers=new Ne(ct,"datasets",!0),this.elements=new Ne(ht,"elements"),this.plugins=new Ne(Object,"plugins"),this.scales=new Ne(Kt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):I(n,a=>{const r=s||this._getRegistryForType(a);this._exec(t,r,a)})})}_exec(t,e,s){const n=Zi(t);z(s["before"+n],[],s),e[t](s),z(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var gt=new Ql;class Jl{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),a=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),a}_notify(t,e,s,n){n=n||{};for(const o of t){const a=o.plugin,r=a[s],l=[e,n,o.options];if(z(r,l,a)===!1&&n.cancelable)return!1}return!0}invalidate(){O(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=D(s.options&&s.options.plugins,{}),o=tc(s);return n===!1&&!e?[]:ic(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,a)=>o.filter(r=>!a.some(l=>r.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function tc(i){const t={},e=[],s=Object.keys(gt.plugins.items);for(let o=0;o<s.length;o++)e.push(gt.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const a=n[o];e.indexOf(a)===-1&&(e.push(a),t[a.id]=!0)}return{plugins:e,localIds:t}}function ec(i,t){return!t&&i===!1?null:i===!0?{}:i}function ic(i,{plugins:t,localIds:e},s,n){const o=[],a=i.getContext();for(const r of t){const l=r.id,c=ec(s[l],n);c!==null&&o.push({plugin:r,options:sc(i.config,{plugin:r,local:e[l]},c,a)})}return o}function sc(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),a=i.getOptionScopes(s,o);return e&&t.defaults&&a.push(t.defaults),i.createResolver(a,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Ii(i,t){const e=N.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function nc(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function oc(i,t){return i===t?"_index_":"_value_"}function Js(i){if(i==="x"||i==="y"||i==="r")return i}function ac(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function Fi(i,...t){if(Js(i))return i;for(const e of t){const s=e.axis||ac(e.position)||i.length>1&&Js(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function tn(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function rc(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return tn(i,"x",e[0])||tn(i,"y",e[0])}return{}}function lc(i,t){const e=Ut[i.type]||{scales:{}},s=t.scales||{},n=Ii(i.type,t),o=Object.create(null);return Object.keys(s).forEach(a=>{const r=s[a];if(!L(r))return console.error(`Invalid scale configuration for scale: ${a}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);const l=Fi(a,r,rc(a,i),N.scales[r.type]),c=oc(l,n),h=e.scales||{};o[a]=me(Object.create(null),[{axis:l},r,h[l],h[c]])}),i.data.datasets.forEach(a=>{const r=a.type||i.type,l=a.indexAxis||Ii(r,t),h=(Ut[r]||{}).scales||{};Object.keys(h).forEach(d=>{const f=nc(d,l),u=a[f+"AxisID"]||f;o[u]=o[u]||Object.create(null),me(o[u],[{axis:f},s[u],h[d]])})}),Object.keys(o).forEach(a=>{const r=o[a];me(r,[N.scales[r.type],N.scale])}),o}function Mo(i){const t=i.options||(i.options={});t.plugins=D(t.plugins,{}),t.scales=lc(i,t)}function ko(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function cc(i){return i=i||{},i.data=ko(i.data),Mo(i),i}const en=new Map,So=new Set;function He(i,t){let e=en.get(i);return e||(e=t(),en.set(i,e),So.add(e)),e}const ce=(i,t,e)=>{const s=Rt(t,e);s!==void 0&&i.add(s)};class hc{constructor(t){this._config=cc(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=ko(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Mo(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return He(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return He(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return He(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return He(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,a=this._cachedScopes(t,s),r=a.get(e);if(r)return r;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>ce(l,t,d))),h.forEach(d=>ce(l,n,d)),h.forEach(d=>ce(l,Ut[o]||{},d)),h.forEach(d=>ce(l,N,d)),h.forEach(d=>ce(l,Li,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),So.has(e)&&a.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,Ut[e]||{},N.datasets[e]||{},{type:e},N,Li]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:a,subPrefixes:r}=sn(this._resolverCache,t,n);let l=a;if(fc(a,e)){o.$shared=!1,s=Et(s)?s():s;const c=this.createResolver(t,s,r);l=ie(a,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=sn(this._resolverCache,t,s);return L(e)?ie(o,e,void 0,n):o}}function sn(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:ss(t,e),subPrefixes:e.filter(r=>!r.toLowerCase().includes("hover"))},s.set(n,o)),o}const dc=i=>L(i)&&Object.getOwnPropertyNames(i).some(t=>Et(i[t]));function fc(i,t){const{isScriptable:e,isIndexable:s}=eo(i);for(const n of t){const o=e(n),a=s(n),r=(a||o)&&i[n];if(o&&(Et(r)||dc(r))||a&&W(r))return!0}return!1}var uc="4.5.0";const gc=["top","bottom","left","right","chartArea"];function nn(i,t){return i==="top"||i==="bottom"||gc.indexOf(i)===-1&&t==="x"}function on(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function an(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),z(e&&e.onComplete,[i],t)}function pc(i){const t=i.chart,e=t.options.animation;z(e&&e.onProgress,[i],t)}function wo(i){return as()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const Qe={},rn=i=>{const t=wo(i);return Object.values(Qe).filter(e=>e.canvas===t).pop()};function mc(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const a=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=a)}}}function bc(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}class pt{static register(...t){gt.add(...t),ln()}static unregister(...t){gt.remove(...t),ln()}constructor(t,e){const s=this.config=new hc(e),n=wo(t),o=rn(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const a=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||Il(n)),this.platform.updateConfig(s);const r=this.platform.acquireContext(n,a.aspectRatio),l=r&&r.canvas,c=l&&l.height,h=l&&l.width;if(this.id=xa(),this.ctx=r,this.canvas=l,this.width=h,this.height=c,this._options=a,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Jl,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Ia(d=>this.update(d),a.resizeDelay||0),this._dataChanges=[],Qe[this.id]=this,!r||!l){console.error("Failed to create chart: can't acquire context from the given item");return}yt.listen(this,"complete",an),yt.listen(this,"progress",pc),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return O(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return gt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():As(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Ps(this.canvas,this.ctx),this}stop(){return yt.stop(this),this}resize(t,e){yt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(n,t,e,o),r=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,As(this,r,!0)&&(this.notifyPlugins("resize",{size:a}),z(s.onResize,[this,a],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};I(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((a,r)=>(a[r]=!1,a),{});let o=[];e&&(o=o.concat(Object.keys(e).map(a=>{const r=e[a],l=Fi(a,r),c=l==="r",h=l==="x";return{options:r,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),I(o,a=>{const r=a.options,l=r.id,c=Fi(l,r),h=D(r.type,a.dtype);(r.position===void 0||nn(r.position,c)!==nn(a.dposition))&&(r.position=a.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const f=gt.getScale(h);d=new f({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(r,t)}),I(n,(a,r)=>{a||delete s[r]}),I(s,a=>{Q.configure(this,a,a.options),Q.addBox(this,a)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(on("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let a=this.getDatasetMeta(s);const r=o.type||this.config.type;if(a.type&&a.type!==r&&(this._destroyDatasetMeta(s),a=this.getDatasetMeta(s)),a.type=r,a.indexAxis=o.indexAxis||Ii(r,this.options),a.order=o.order||0,a.index=s,a.label=""+o.label,a.visible=this.isDatasetVisible(s),a.controller)a.controller.updateIndex(s),a.controller.linkScales();else{const l=gt.getController(r),{datasetElementType:c,dataElementType:h}=N.datasets[r];Object.assign(l,{dataElementType:gt.getElement(h),datasetElementType:c&&gt.getElement(c)}),a.controller=new l(this,s),t.push(a.controller)}}return this._updateMetasets(),t}_resetElements(){I(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let a=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),f=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(f),a=Math.max(+d.getMaxOverflow(),a)}a=this._minPadding=s.layout.autoPadding?a:0,this._updateLayout(a),n||I(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(on("z","_idx"));const{_active:r,_lastEvent:l}=this;l?this._eventHandler(l,!0):r.length&&this._updateHoverStyles(r,r,!0),this.render()}_updateScales(){I(this.scales,t=>{Q.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!bs(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const a=s==="_removeElements"?-o:o;mc(t,n,a)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(a=>a[0]===o).map((a,r)=>r+","+a.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!bs(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Q.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],I(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,Et(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(yt.has(this)?this.attached&&!yt.running(this)&&yt.start(this):(this.draw(),an({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const a=e[n];(!t||a.visible)&&s.push(a)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s={meta:t,index:t.index,cancelable:!0},n=uo(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(n&&hi(e,n),t.controller.draw(),n&&di(e),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return wt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=gl.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=It(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),a=o.controller._resolveAnimations(void 0,n);ke(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),a.update(o,{visible:s}),this.update(r=>r.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),yt.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Ps(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete Qe[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,a)=>{e.addEventListener(this,o,a),t[o]=a},n=(o,a,r)=>{o.offsetX=a,o.offsetY=r,this._eventHandler(o)};I(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let a;const r=()=>{n("attach",r),this.attached=!0,this.resize(),s("resize",o),s("detach",a)};a=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",r)},e.isAttached(this.canvas)?r():a()}unbindEvents(){I(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},I(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,a,r,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),r=0,l=t.length;r<l;++r){a=t[r];const c=a&&this.getDatasetMeta(a.datasetIndex).controller;c&&c[n+"HoverStyle"](a.element,a.datasetIndex,a.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:a})=>{const r=this.getDatasetMeta(o);if(!r)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:r.data[a],index:a}});!ei(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),a=o(e,t),r=s?t:o(t,e);a.length&&this.updateHoverStyle(a,n.mode,!1),r.length&&n.mode&&this.updateHoverStyle(r,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=a=>(a.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,a=e,r=this._getActiveElements(t,n,s,a),l=Sa(t),c=bc(t,this._lastEvent,s,l);s&&(this._lastEvent=null,z(o.onHover,[t,r,this],this),l&&z(o.onClick,[t,r,this],this));const h=!ei(r,n);return(h||e)&&(this._active=r,this._updateHoverStyles(r,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}}k(pt,"defaults",N),k(pt,"instances",Qe),k(pt,"overrides",Ut),k(pt,"registry",gt),k(pt,"version",uc),k(pt,"getChart",rn);function ln(){return I(pt.instances,i=>i._plugins.invalidate())}function xc(i,t,e){const{startAngle:s,x:n,y:o,outerRadius:a,innerRadius:r,options:l}=t,{borderWidth:c,borderJoinStyle:h}=l,d=Math.min(c/a,Z(s-e));if(i.beginPath(),i.arc(n,o,a-c/2,s+d/2,e-d/2),r>0){const f=Math.min(c/r,Z(s-e));i.arc(n,o,r+c/2,e-f/2,s+f/2,!0)}else{const f=Math.min(c/2,a*Z(s-e));if(h==="round")i.arc(n,o,f,e-R/2,s+R/2,!0);else if(h==="bevel"){const u=2*f*f,g=-u*Math.cos(e+R/2)+n,p=-u*Math.sin(e+R/2)+o,m=u*Math.cos(s+R/2)+n,b=u*Math.sin(s+R/2)+o;i.lineTo(g,p),i.lineTo(m,b)}}i.closePath(),i.moveTo(0,0),i.rect(0,0,i.canvas.width,i.canvas.height),i.clip("evenodd")}function _c(i,t,e){const{startAngle:s,pixelMargin:n,x:o,y:a,outerRadius:r,innerRadius:l}=t;let c=n/r;i.beginPath(),i.arc(o,a,r,s-c,e+c),l>n?(c=n/l,i.arc(o,a,l,e+c,s-c,!0)):i.arc(o,a,n,e+Y,s-Y),i.closePath(),i.clip()}function yc(i){return is(i,["outerStart","outerEnd","innerStart","innerEnd"])}function vc(i,t,e,s){const n=yc(i.options.borderRadius),o=(e-t)/2,a=Math.min(o,s*t/2),r=l=>{const c=(e-Math.min(o,l))*s/2;return K(l,0,Math.min(o,c))};return{outerStart:r(n.outerStart),outerEnd:r(n.outerEnd),innerStart:K(n.innerStart,0,a),innerEnd:K(n.innerEnd,0,a)}}function Qt(i,t,e,s){return{x:e+i*Math.cos(t),y:s+i*Math.sin(t)}}function ri(i,t,e,s,n,o){const{x:a,y:r,startAngle:l,pixelMargin:c,innerRadius:h}=t,d=Math.max(t.outerRadius+s+e-c,0),f=h>0?h+s+e+c:0;let u=0;const g=n-l;if(s){const E=h>0?h-s:0,F=d>0?d-s:0,B=(E+F)/2,it=B!==0?g*B/(B+s):g;u=(g-it)/2}const p=Math.max(.001,g*d-e/R)/d,m=(g-p)/2,b=l+m+u,x=n-m-u,{outerStart:y,outerEnd:v,innerStart:_,innerEnd:M}=vc(t,f,d,x-b),S=d-y,w=d-v,P=b+y/S,C=x-v/w,A=f+_,T=f+M,$=b+_/A,q=x-M/T;if(i.beginPath(),o){const E=(P+C)/2;if(i.arc(a,r,d,P,E),i.arc(a,r,d,E,C),v>0){const X=Qt(w,C,a,r);i.arc(X.x,X.y,v,C,x+Y)}const F=Qt(T,x,a,r);if(i.lineTo(F.x,F.y),M>0){const X=Qt(T,q,a,r);i.arc(X.x,X.y,M,x+Y,q+Math.PI)}const B=(x-M/f+(b+_/f))/2;if(i.arc(a,r,f,x-M/f,B,!0),i.arc(a,r,f,B,b+_/f,!0),_>0){const X=Qt(A,$,a,r);i.arc(X.x,X.y,_,$+Math.PI,b-Y)}const it=Qt(S,b,a,r);if(i.lineTo(it.x,it.y),y>0){const X=Qt(S,P,a,r);i.arc(X.x,X.y,y,b-Y,P)}}else{i.moveTo(a,r);const E=Math.cos(P)*d+a,F=Math.sin(P)*d+r;i.lineTo(E,F);const B=Math.cos(C)*d+a,it=Math.sin(C)*d+r;i.lineTo(B,it)}i.closePath()}function Mc(i,t,e,s,n){const{fullCircles:o,startAngle:a,circumference:r}=t;let l=t.endAngle;if(o){ri(i,t,e,s,l,n);for(let c=0;c<o;++c)i.fill();isNaN(r)||(l=a+(r%V||V))}return ri(i,t,e,s,l,n),i.fill(),l}function kc(i,t,e,s,n){const{fullCircles:o,startAngle:a,circumference:r,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:f,borderRadius:u}=l,g=l.borderAlign==="inner";if(!c)return;i.setLineDash(d||[]),i.lineDashOffset=f,g?(i.lineWidth=c*2,i.lineJoin=h||"round"):(i.lineWidth=c,i.lineJoin=h||"bevel");let p=t.endAngle;if(o){ri(i,t,e,s,p,n);for(let m=0;m<o;++m)i.stroke();isNaN(r)||(p=a+(r%V||V))}g&&_c(i,t,p),l.selfJoin&&p-a>=R&&u===0&&h!=="miter"&&xc(i,t,p),o||(ri(i,t,e,s,p,n),i.stroke())}class ue extends ht{constructor(e){super();k(this,"circumference");k(this,"endAngle");k(this,"fullCircles");k(this,"innerRadius");k(this,"outerRadius");k(this,"pixelMargin");k(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.getProps(["x","y"],n),{angle:a,distance:r}=$n(o,{x:e,y:s}),{startAngle:l,endAngle:c,innerRadius:h,outerRadius:d,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],n),u=(this.options.spacing+this.options.borderWidth)/2,g=D(f,c-l),p=Se(a,l,c)&&l!==c,m=g>=V||p,b=kt(r,h+u,d+u);return m&&b}getCenterPoint(e){const{x:s,y:n,startAngle:o,endAngle:a,innerRadius:r,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],e),{offset:c,spacing:h}=this.options,d=(o+a)/2,f=(r+l+h+c)/2;return{x:s+Math.cos(d)*f,y:n+Math.sin(d)*f}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){const{options:s,circumference:n}=this,o=(s.offset||0)/4,a=(s.spacing||0)/2,r=s.circular;if(this.pixelMargin=s.borderAlign==="inner"?.33:0,this.fullCircles=n>V?Math.floor(n/V):0,n===0||this.innerRadius<0||this.outerRadius<0)return;e.save();const l=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(R,n||0)),h=o*c;e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,Mc(e,this,h,a,r),kc(e,this,h,a,r),e.restore()}}k(ue,"id","arc"),k(ue,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1}),k(ue,"defaultRoutes",{backgroundColor:"backgroundColor"}),k(ue,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"});function Po(i,t,e=t){i.lineCap=D(e.borderCapStyle,t.borderCapStyle),i.setLineDash(D(e.borderDash,t.borderDash)),i.lineDashOffset=D(e.borderDashOffset,t.borderDashOffset),i.lineJoin=D(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=D(e.borderWidth,t.borderWidth),i.strokeStyle=D(e.borderColor,t.borderColor)}function Sc(i,t,e){i.lineTo(e.x,e.y)}function wc(i){return i.stepped?Xa:i.tension||i.cubicInterpolationMode==="monotone"?Ka:Sc}function Do(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:a,end:r}=t,l=Math.max(n,a),c=Math.min(o,r),h=n<a&&o<a||n>r&&o>r;return{count:s,start:l,loop:t.loop,ilen:c<l&&!h?s+c-l:c-l}}function Pc(i,t,e,s){const{points:n,options:o}=t,{count:a,start:r,loop:l,ilen:c}=Do(n,e,s),h=wc(o);let{move:d=!0,reverse:f}=s||{},u,g,p;for(u=0;u<=c;++u)g=n[(r+(f?c-u:u))%a],!g.skip&&(d?(i.moveTo(g.x,g.y),d=!1):h(i,p,g,f,o.stepped),p=g);return l&&(g=n[(r+(f?c:0))%a],h(i,p,g,f,o.stepped)),!!l}function Dc(i,t,e,s){const n=t.points,{count:o,start:a,ilen:r}=Do(n,e,s),{move:l=!0,reverse:c}=s||{};let h=0,d=0,f,u,g,p,m,b;const x=v=>(a+(c?r-v:v))%o,y=()=>{p!==m&&(i.lineTo(h,m),i.lineTo(h,p),i.lineTo(h,b))};for(l&&(u=n[x(0)],i.moveTo(u.x,u.y)),f=0;f<=r;++f){if(u=n[x(f)],u.skip)continue;const v=u.x,_=u.y,M=v|0;M===g?(_<p?p=_:_>m&&(m=_),h=(d*h+v)/++d):(y(),i.lineTo(v,_),g=M,d=0,p=m=_),b=_}y()}function zi(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Dc:Pc}function Cc(i){return i.stepped?Pr:i.tension||i.cubicInterpolationMode==="monotone"?Dr:Ht}function Ac(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),Po(i,t.options),i.stroke(n)}function Oc(i,t,e,s){const{segments:n,options:o}=t,a=zi(t);for(const r of n)Po(i,o,r.style),i.beginPath(),a(i,t,r,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const Tc=typeof Path2D=="function";function Lc(i,t,e,s){Tc&&!t.options.segment?Ac(i,t,e,s):Oc(i,t,e,s)}class Ot extends ht{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;xr(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Rr(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,a=fo(this,{property:e,start:n,end:n});if(!a.length)return;const r=[],l=Cc(s);let c,h;for(c=0,h=a.length;c<h;++c){const{start:d,end:f}=a[c],u=o[d],g=o[f];if(u===g){r.push(u);continue}const p=Math.abs((n-u[e])/(g[e]-u[e])),m=l(u,g,p,s.stepped);m[e]=t[e],r.push(m)}return r.length===1?r[0]:r}pathSegment(t,e,s){return zi(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=zi(this);let a=this._loop;e=e||0,s=s||this.points.length-e;for(const r of n)a&=o(t,this,r,{start:e,end:e+s-1});return!!a}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),Lc(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}k(Ot,"id","line"),k(Ot,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),k(Ot,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),k(Ot,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function cn(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class Je extends ht{constructor(e){super();k(this,"parsed");k(this,"skip");k(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.options,{x:a,y:r}=this.getProps(["x","y"],n);return Math.pow(e-a,2)+Math.pow(s-r,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,s){return cn(this,e,"x",s)}inYRange(e,s){return cn(this,e,"y",s)}getCenterPoint(e){const{x:s,y:n}=this.getProps(["x","y"],e);return{x:s,y:n}}size(e){e=e||this.options||{};let s=e.radius||0;s=Math.max(s,s&&e.hoverRadius||0);const n=s&&e.borderWidth||0;return(s+n)*2}draw(e,s){const n=this.options;this.skip||n.radius<.1||!wt(this,s,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,Ri(e,n,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}k(Je,"id","point"),k(Je,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),k(Je,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Co(i,t){const{x:e,y:s,base:n,width:o,height:a}=i.getProps(["x","y","base","width","height"],t);let r,l,c,h,d;return i.horizontal?(d=a/2,r=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,r=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:r,top:c,right:l,bottom:h}}function Tt(i,t,e,s){return i?0:K(t,e,s)}function Rc(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=to(s);return{t:Tt(n.top,o.top,0,e),r:Tt(n.right,o.right,0,t),b:Tt(n.bottom,o.bottom,0,e),l:Tt(n.left,o.left,0,t)}}function Ec(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Yt(n),a=Math.min(t,e),r=i.borderSkipped,l=s||L(n);return{topLeft:Tt(!l||r.top||r.left,o.topLeft,0,a),topRight:Tt(!l||r.top||r.right,o.topRight,0,a),bottomLeft:Tt(!l||r.bottom||r.left,o.bottomLeft,0,a),bottomRight:Tt(!l||r.bottom||r.right,o.bottomRight,0,a)}}function Ic(i){const t=Co(i),e=t.right-t.left,s=t.bottom-t.top,n=Rc(i,e/2,s/2),o=Ec(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function Pi(i,t,e,s){const n=t===null,o=e===null,r=i&&!(n&&o)&&Co(i,s);return r&&(n||kt(t,r.left,r.right))&&(o||kt(e,r.top,r.bottom))}function Fc(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function zc(i,t){i.rect(t.x,t.y,t.w,t.h)}function Di(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,a=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+a,radius:i.radius}}class ti extends ht{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:a}=Ic(this),r=Fc(a.radius)?we:zc;t.save(),(a.w!==o.w||a.h!==o.h)&&(t.beginPath(),r(t,Di(a,e,o)),t.clip(),r(t,Di(o,-e,a)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),r(t,Di(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return Pi(this,t,e,s)}inXRange(t,e){return Pi(this,t,null,e)}inYRange(t,e){return Pi(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}k(ti,"id","bar"),k(ti,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),k(ti,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var Bc=Object.freeze({__proto__:null,ArcElement:ue,BarElement:ti,LineElement:Ot,PointElement:Je});const Bi=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],hn=Bi.map(i=>i.replace("rgb(","rgba(").replace(")",", 0.5)"));function Ao(i){return Bi[i%Bi.length]}function Oo(i){return hn[i%hn.length]}function Vc(i,t){return i.borderColor=Ao(t),i.backgroundColor=Oo(t),++t}function Wc(i,t){return i.backgroundColor=i.data.map(()=>Ao(t++)),t}function Nc(i,t){return i.backgroundColor=i.data.map(()=>Oo(t++)),t}function Hc(i){let t=0;return(e,s)=>{const n=i.getDatasetMeta(s).controller;n instanceof jt?t=Wc(e,t):n instanceof ye?t=Nc(e,t):n&&(t=Vc(e,t))}}function dn(i){let t;for(t in i)if(i[t].borderColor||i[t].backgroundColor)return!0;return!1}function jc(i){return i&&(i.borderColor||i.backgroundColor)}function Yc(){return N.borderColor!=="rgba(0,0,0,0.1)"||N.backgroundColor!=="rgba(0,0,0,0.1)"}var $c={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(i,t,e){if(!e.enabled)return;const{data:{datasets:s},options:n}=i.config,{elements:o}=n,a=dn(s)||jc(n)||o&&dn(o)||Yc();if(!e.forceOverride&&a)return;const r=Hc(i);s.forEach(r)}};function Uc(i,t,e,s,n){const o=n.samples||s;if(o>=e)return i.slice(t,t+e);const a=[],r=(e-2)/(o-2);let l=0;const c=t+e-1;let h=t,d,f,u,g,p;for(a[l++]=i[h],d=0;d<o-2;d++){let m=0,b=0,x;const y=Math.floor((d+1)*r)+1+t,v=Math.min(Math.floor((d+2)*r)+1,e)+t,_=v-y;for(x=y;x<v;x++)m+=i[x].x,b+=i[x].y;m/=_,b/=_;const M=Math.floor(d*r)+1+t,S=Math.min(Math.floor((d+1)*r)+1,e)+t,{x:w,y:P}=i[h];for(u=g=-1,x=M;x<S;x++)g=.5*Math.abs((w-m)*(i[x].y-P)-(w-i[x].x)*(b-P)),g>u&&(u=g,f=i[x],p=x);a[l++]=f,h=p}return a[l++]=i[c],a}function Xc(i,t,e,s){let n=0,o=0,a,r,l,c,h,d,f,u,g,p;const m=[],b=t+e-1,x=i[t].x,v=i[b].x-x;for(a=t;a<t+e;++a){r=i[a],l=(r.x-x)/v*s,c=r.y;const _=l|0;if(_===h)c<g?(g=c,d=a):c>p&&(p=c,f=a),n=(o*n+r.x)/++o;else{const M=a-1;if(!O(d)&&!O(f)){const S=Math.min(d,f),w=Math.max(d,f);S!==u&&S!==M&&m.push({...i[S],x:n}),w!==u&&w!==M&&m.push({...i[w],x:n})}a>0&&M!==u&&m.push(i[M]),m.push(r),h=_,o=0,g=p=c,d=f=u=a}}return m}function To(i){if(i._decimated){const t=i._data;delete i._decimated,delete i._data,Object.defineProperty(i,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function fn(i){i.data.datasets.forEach(t=>{To(t)})}function Kc(i,t){const e=t.length;let s=0,n;const{iScale:o}=i,{min:a,max:r,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=K(St(t,o.axis,a).lo,0,e-1)),c?n=K(St(t,o.axis,r).hi+1,s,e)-s:n=e-s,{start:s,count:n}}var qc={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(i,t,e)=>{if(!e.enabled){fn(i);return}const s=i.width;i.data.datasets.forEach((n,o)=>{const{_data:a,indexAxis:r}=n,l=i.getDatasetMeta(o),c=a||n.data;if(de([r,i.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const h=i.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||i.options.parsing)return;let{start:d,count:f}=Kc(l,c);const u=e.threshold||4*s;if(f<=u){To(n);return}O(a)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(p){this._data=p}}));let g;switch(e.algorithm){case"lttb":g=Uc(c,d,f,s,e);break;case"min-max":g=Xc(c,d,f,s);break;default:throw new Error(`Unsupported decimation algorithm '${e.algorithm}'`)}n._decimated=g})},destroy(i){fn(i)}};function Gc(i,t,e){const s=i.segments,n=i.points,o=t.points,a=[];for(const r of s){let{start:l,end:c}=r;c=gi(l,c,n);const h=Vi(e,n[l],n[c],r.loop);if(!t.segments){a.push({source:r,target:h,start:n[l],end:n[c]});continue}const d=fo(t,h);for(const f of d){const u=Vi(e,o[f.start],o[f.end],f.loop),g=ho(r,n,u);for(const p of g)a.push({source:p,target:f,start:{[e]:un(h,u,"start",Math.max)},end:{[e]:un(h,u,"end",Math.min)}})}}return a}function Vi(i,t,e,s){if(s)return;let n=t[i],o=e[i];return i==="angle"&&(n=Z(n),o=Z(o)),{property:i,start:n,end:o}}function Zc(i,t){const{x:e=null,y:s=null}=i||{},n=t.points,o=[];return t.segments.forEach(({start:a,end:r})=>{r=gi(a,r,n);const l=n[a],c=n[r];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function gi(i,t,e){for(;t>i;t--){const s=e[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function un(i,t,e,s){return i&&t?s(i[e],t[e]):i?i[e]:t?t[e]:0}function Lo(i,t){let e=[],s=!1;return W(i)?(s=!0,e=i):e=Zc(i,t),e.length?new Ot({points:e,options:{tension:0},_loop:s,_fullLoop:s}):null}function gn(i){return i&&i.fill!==!1}function Qc(i,t,e){let n=i[t].fill;const o=[t];let a;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!H(n))return n;if(a=i[n],!a)return!1;if(a.visible)return n;o.push(n),n=a.fill}return!1}function Jc(i,t,e){const s=sh(i);if(L(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return H(n)&&Math.floor(n)===n?th(s[0],t,n,e):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function th(i,t,e,s){return(i==="-"||i==="+")&&(e=t+e),e===t||e<0||e>=s?!1:e}function eh(i,t){let e=null;return i==="start"?e=t.bottom:i==="end"?e=t.top:L(i)?e=t.getPixelForValue(i.value):t.getBasePixel&&(e=t.getBasePixel()),e}function ih(i,t,e){let s;return i==="start"?s=e:i==="end"?s=t.options.reverse?t.min:t.max:L(i)?s=i.value:s=t.getBaseValue(),s}function sh(i){const t=i.options,e=t.fill;let s=D(e&&e.target,e);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function nh(i){const{scale:t,index:e,line:s}=i,n=[],o=s.segments,a=s.points,r=oh(t,e);r.push(Lo({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)ah(n,a[h],r)}return new Ot({points:n,options:{}})}function oh(i,t){const e=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function ah(i,t,e){const s=[];for(let n=0;n<e.length;n++){const o=e[n],{first:a,last:r,point:l}=rh(o,t,"x");if(!(!l||a&&r)){if(a)s.unshift(l);else if(i.push(l),!r)break}}i.push(...s)}function rh(i,t,e){const s=i.interpolate(t,e);if(!s)return{};const n=s[e],o=i.segments,a=i.points;let r=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=a[h.start][e],f=a[h.end][e];if(kt(n,d,f)){r=n===d,l=n===f;break}}return{first:r,last:l,point:s}}class Ro{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,s){const{x:n,y:o,radius:a}=this;return e=e||{start:0,end:V},t.arc(n,o,a,e.end,e.start,!0),!s.bounds}interpolate(t){const{x:e,y:s,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function lh(i){const{chart:t,fill:e,line:s}=i;if(H(e))return ch(t,e);if(e==="stack")return nh(i);if(e==="shape")return!0;const n=hh(i);return n instanceof Ro?n:Lo(n,s)}function ch(i,t){const e=i.getDatasetMeta(t);return e&&i.isDatasetVisible(t)?e.dataset:null}function hh(i){return(i.scale||{}).getPointPositionForValue?fh(i):dh(i)}function dh(i){const{scale:t={},fill:e}=i,s=eh(e,t);if(H(s)){const n=t.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function fh(i){const{scale:t,fill:e}=i,s=t.options,n=t.getLabels().length,o=s.reverse?t.max:t.min,a=ih(e,t,o),r=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new Ro({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(a)})}for(let l=0;l<n;++l)r.push(t.getPointPositionForValue(l,a));return r}function Ci(i,t,e){const s=lh(t),{chart:n,index:o,line:a,scale:r,axis:l}=t,c=a.options,h=c.fill,d=c.backgroundColor,{above:f=d,below:u=d}=h||{},g=n.getDatasetMeta(o),p=uo(n,g);s&&a.points.length&&(hi(i,e),uh(i,{line:a,target:s,above:f,below:u,area:e,scale:r,axis:l,clip:p}),di(i))}function uh(i,t){const{line:e,target:s,above:n,below:o,area:a,scale:r,clip:l}=t,c=e._loop?"angle":t.axis;i.save();let h=o;o!==n&&(c==="x"?(pn(i,s,a.top),Ai(i,{line:e,target:s,color:n,scale:r,property:c,clip:l}),i.restore(),i.save(),pn(i,s,a.bottom)):c==="y"&&(mn(i,s,a.left),Ai(i,{line:e,target:s,color:o,scale:r,property:c,clip:l}),i.restore(),i.save(),mn(i,s,a.right),h=n)),Ai(i,{line:e,target:s,color:h,scale:r,property:c,clip:l}),i.restore()}function pn(i,t,e){const{segments:s,points:n}=t;let o=!0,a=!1;i.beginPath();for(const r of s){const{start:l,end:c}=r,h=n[l],d=n[gi(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,e),i.lineTo(h.x,h.y)),a=!!t.pathSegment(i,r,{move:a}),a?i.closePath():i.lineTo(d.x,e)}i.lineTo(t.first().x,e),i.closePath(),i.clip()}function mn(i,t,e){const{segments:s,points:n}=t;let o=!0,a=!1;i.beginPath();for(const r of s){const{start:l,end:c}=r,h=n[l],d=n[gi(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(e,h.y),i.lineTo(h.x,h.y)),a=!!t.pathSegment(i,r,{move:a}),a?i.closePath():i.lineTo(e,d.y)}i.lineTo(e,t.first().y),i.closePath(),i.clip()}function Ai(i,t){const{line:e,target:s,property:n,color:o,scale:a,clip:r}=t,l=Gc(e,s,n);for(const{source:c,target:h,start:d,end:f}of l){const{style:{backgroundColor:u=o}={}}=c,g=s!==!0;i.save(),i.fillStyle=u,gh(i,a,r,g&&Vi(n,d,f)),i.beginPath();const p=!!e.pathSegment(i,c);let m;if(g){p?i.closePath():bn(i,s,f,n);const b=!!s.pathSegment(i,h,{move:p,reverse:!0});m=p&&b,m||bn(i,s,d,n)}i.closePath(),i.fill(m?"evenodd":"nonzero"),i.restore()}}function gh(i,t,e,s){const n=t.chart.chartArea,{property:o,start:a,end:r}=s||{};if(o==="x"||o==="y"){let l,c,h,d;o==="x"?(l=a,c=n.top,h=r,d=n.bottom):(l=n.left,c=a,h=n.right,d=r),i.beginPath(),e&&(l=Math.max(l,e.left),h=Math.min(h,e.right),c=Math.max(c,e.top),d=Math.min(d,e.bottom)),i.rect(l,c,h-l,d-c),i.clip()}}function bn(i,t,e,s){const n=t.interpolate(e,s);n&&i.lineTo(n.x,n.y)}var ph={id:"filler",afterDatasetsUpdate(i,t,e){const s=(i.data.datasets||[]).length,n=[];let o,a,r,l;for(a=0;a<s;++a)o=i.getDatasetMeta(a),r=o.dataset,l=null,r&&r.options&&r instanceof Ot&&(l={visible:i.isDatasetVisible(a),index:a,fill:Jc(r,a,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:r}),o.$filler=l,n.push(l);for(a=0;a<s;++a)l=n[a],!(!l||l.fill===!1)&&(l.fill=Qc(n,a,e.propagate))},beforeDraw(i,t,e){const s=e.drawTime==="beforeDraw",n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let a=n.length-1;a>=0;--a){const r=n[a].$filler;r&&(r.line.updateControlPoints(o,r.axis),s&&r.fill&&Ci(i.ctx,r,o))}},beforeDatasetsDraw(i,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;gn(o)&&Ci(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,t,e){const s=t.meta.$filler;!gn(s)||e.drawTime!=="beforeDatasetDraw"||Ci(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const xn=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},mh=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class _n extends ht{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=z(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=U(s.font),o=n.size,a=this._computeTitleHeight(),{boxWidth:r,itemHeight:l}=xn(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(a,o,r,l)+10):(h=this.maxHeight,c=this._fitCols(a,n,r,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+r;let d=t;o.textAlign="left",o.textBaseline="middle";let f=-1,u=-h;return this.legendItems.forEach((g,p)=>{const m=s+e/2+o.measureText(g.text).width;(p===0||c[c.length-1]+m+2*r>a)&&(d+=h,c[c.length-(p>0?0:1)]=0,u+=h,f++),l[p]={left:0,top:u,row:f,width:m,height:n},c[c.length-1]+=m+r}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=a-t;let d=r,f=0,u=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:x,itemHeight:y}=bh(s,e,o,m,n);b>0&&u+y+2*r>h&&(d+=f+r,c.push({width:f,height:u}),g+=f+r,p++,f=u=0),l[b]={left:g,top:u,col:p,width:x,height:y},f=Math.max(f,x),u+=y+r}),d+=f,c.push({width:f,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,a=te(o,this.left,this.width);if(this.isHorizontal()){let r=0,l=G(s,this.left+n,this.right-this.lineWidths[r]);for(const c of e)r!==c.row&&(r=c.row,l=G(s,this.left+n,this.right-this.lineWidths[r])),c.top+=this.top+t+n,c.left=a.leftForLtr(a.x(l),c.width),l+=c.width+n}else{let r=0,l=G(s,this.top+t+n,this.bottom-this.columnSizes[r].height);for(const c of e)c.col!==r&&(r=c.col,l=G(s,this.top+t+n,this.bottom-this.columnSizes[r].height)),c.top=l,c.left+=this.left+n,c.left=a.leftForLtr(a.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;hi(t,this),this._draw(),di(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:a}=t,r=N.color,l=te(t.rtl,this.left,this.width),c=U(a.font),{padding:h}=a,d=c.size,f=d/2;let u;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:g,boxHeight:p,itemHeight:m}=xn(a,d),b=function(M,S,w){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();const P=D(w.lineWidth,1);if(n.fillStyle=D(w.fillStyle,r),n.lineCap=D(w.lineCap,"butt"),n.lineDashOffset=D(w.lineDashOffset,0),n.lineJoin=D(w.lineJoin,"miter"),n.lineWidth=P,n.strokeStyle=D(w.strokeStyle,r),n.setLineDash(D(w.lineDash,[])),a.usePointStyle){const C={radius:p*Math.SQRT2/2,pointStyle:w.pointStyle,rotation:w.rotation,borderWidth:P},A=l.xPlus(M,g/2),T=S+f;Jn(n,C,A,T,a.pointStyleWidth&&g)}else{const C=S+Math.max((d-p)/2,0),A=l.leftForLtr(M,g),T=Yt(w.borderRadius);n.beginPath(),Object.values(T).some($=>$!==0)?we(n,{x:A,y:C,w:g,h:p,radius:T}):n.rect(A,C,g,p),n.fill(),P!==0&&n.stroke()}n.restore()},x=function(M,S,w){Xt(n,w.text,M,S+m/2,c,{strikethrough:w.hidden,textAlign:l.textAlign(w.textAlign)})},y=this.isHorizontal(),v=this._computeTitleHeight();y?u={x:G(o,this.left+h,this.right-s[0]),y:this.top+h+v,line:0}:u={x:this.left+h,y:G(o,this.top+v+h,this.bottom-e[0].height),line:0},ro(this.ctx,t.textDirection);const _=m+h;this.legendItems.forEach((M,S)=>{n.strokeStyle=M.fontColor,n.fillStyle=M.fontColor;const w=n.measureText(M.text).width,P=l.textAlign(M.textAlign||(M.textAlign=a.textAlign)),C=g+f+w;let A=u.x,T=u.y;l.setWidth(this.width),y?S>0&&A+C+h>this.right&&(T=u.y+=_,u.line++,A=u.x=G(o,this.left+h,this.right-s[u.line])):S>0&&T+_>this.bottom&&(A=u.x=A+e[u.line].width+h,u.line++,T=u.y=G(o,this.top+v+h,this.bottom-e[u.line].height));const $=l.x(A);if(b($,T,M),A=Fa(P,A+g+f,y?A+C:this.right,t.rtl),x(l.x(A),T,M),y)u.x+=C+h;else if(typeof M.text!="string"){const q=c.lineHeight;u.y+=Eo(M,q)+h}else u.y+=_}),lo(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=U(e.font),n=J(e.padding);if(!e.display)return;const o=te(t.rtl,this.left,this.width),a=this.ctx,r=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),h=this.top+c,d=G(t.align,d,this.right-f);else{const g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+G(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}const u=G(r,d,d+f);a.textAlign=o.textAlign(ts(r)),a.textBaseline="middle",a.strokeStyle=e.color,a.fillStyle=e.color,a.font=s.string,Xt(a,e.text,u,h,s)}_computeTitleHeight(){const t=this.options.title,e=U(t.font),s=J(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(kt(t,this.left,this.right)&&kt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],kt(t,n.left,n.left+n.width)&&kt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!yh(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=mh(n,s);n&&!o&&z(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&z(e.onHover,[t,s,this],this)}else s&&z(e.onClick,[t,s,this],this)}}function bh(i,t,e,s,n){const o=xh(s,i,t,e),a=_h(n,s,t.lineHeight);return{itemWidth:o,itemHeight:a}}function xh(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,a)=>o.length>a.length?o:a)),t+e.size/2+s.measureText(n).width}function _h(i,t,e){let s=i;return typeof t.text!="string"&&(s=Eo(t,e)),s}function Eo(i,t){const e=i.text?i.text.length:0;return t*e}function yh(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var vh={id:"legend",_element:_n,start(i,t,e){const s=i.legend=new _n({ctx:i.ctx,options:e,chart:i});Q.configure(i,s,e),Q.addBox(i,s)},stop(i){Q.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;Q.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:a,borderRadius:r}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=J(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:a&&(r||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class cs extends ht{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=W(s.text)?s.text.length:1;this._padding=J(s.padding);const o=n*U(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:a}=this,r=a.align;let l=0,c,h,d;return this.isHorizontal()?(h=G(r,s,o),d=e+t,c=o-s):(a.position==="left"?(h=s+t,d=G(r,n,e),l=R*-.5):(h=o-t,d=G(r,e,n),l=R*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=U(e.font),o=s.lineHeight/2+this._padding.top,{titleX:a,titleY:r,maxWidth:l,rotation:c}=this._drawArgs(o);Xt(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:ts(e.align),textBaseline:"middle",translation:[a,r]})}}function Mh(i,t){const e=new cs({ctx:i.ctx,options:t,chart:i});Q.configure(i,e,t),Q.addBox(i,e),i.titleBlock=e}var kh={id:"title",_element:cs,start(i,t,e){Mh(i,e)},stop(i){const t=i.titleBlock;Q.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;Q.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const je=new WeakMap;var Sh={id:"subtitle",start(i,t,e){const s=new cs({ctx:i.ctx,options:e,chart:i});Q.configure(i,s,e),Q.addBox(i,s),je.set(i,s)},stop(i){Q.removeBox(i,je.get(i)),je.delete(i)},beforeUpdate(i,t,e){const s=je.get(i);Q.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ge={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const r=i[t].element;if(r&&r.hasValue()){const l=r.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((r,l)=>r+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,a,r;for(o=0,a=i.length;o<a;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=Ti(t,c);h<n&&(n=h,r=l)}}if(r){const l=r.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function ut(i,t){return t&&(W(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function vt(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function wh(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:a,value:r}=o.getLabelAndValue(n);return{chart:i,label:a,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:r,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function yn(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:a,boxHeight:r}=t,l=U(t.bodyFont),c=U(t.titleFont),h=U(t.footerFont),d=o.length,f=n.length,u=s.length,g=J(t.padding);let p=g.height,m=0,b=s.reduce((v,_)=>v+_.before.length+_.lines.length+_.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const v=t.displayColors?Math.max(r,l.lineHeight):l.lineHeight;p+=u*v+(b-u)*l.lineHeight+(b-1)*t.bodySpacing}f&&(p+=t.footerMarginTop+f*h.lineHeight+(f-1)*t.footerSpacing);let x=0;const y=function(v){m=Math.max(m,e.measureText(v).width+x)};return e.save(),e.font=c.string,I(i.title,y),e.font=l.string,I(i.beforeBody.concat(i.afterBody),y),x=t.displayColors?a+2+t.boxPadding:0,I(s,v=>{I(v.before,y),I(v.lines,y),I(v.after,y)}),x=0,e.font=h.string,I(i.footer,y),e.restore(),m+=g.width,{width:m,height:p}}function Ph(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function Dh(i,t,e,s){const{x:n,width:o}=s,a=e.caretSize+e.caretPadding;if(i==="left"&&n+o+a>t.width||i==="right"&&n-o-a<0)return!0}function Ch(i,t,e,s){const{x:n,width:o}=e,{width:a,chartArea:{left:r,right:l}}=i;let c="center";return s==="center"?c=n<=(r+l)/2?"left":"right":n<=o/2?c="left":n>=a-o/2&&(c="right"),Dh(c,i,t,e)&&(c="center"),c}function vn(i,t,e){const s=e.yAlign||t.yAlign||Ph(i,e);return{xAlign:e.xAlign||t.xAlign||Ch(i,t,e,s),yAlign:s}}function Ah(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function Oh(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function Mn(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:a}=i,{xAlign:r,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:f,bottomRight:u}=Yt(a);let g=Ah(t,r);const p=Oh(t,l,c);return l==="center"?r==="left"?g+=c:r==="right"&&(g-=c):r==="left"?g-=Math.max(h,f)+n:r==="right"&&(g+=Math.max(d,u)+n),{x:K(g,0,s.width-t.width),y:K(p,0,s.height-t.height)}}function Ye(i,t,e){const s=J(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function kn(i){return ut([],vt(i))}function Th(i,t,e){return It(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function Sn(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const Io={beforeTitle:_t,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:_t,beforeBody:_t,beforeLabel:_t,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return O(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:_t,afterBody:_t,beforeFooter:_t,footer:_t,afterFooter:_t};function tt(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?Io[t].call(e,s):n}class Wi extends ht{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new go(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=Th(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=tt(s,"beforeTitle",this,t),o=tt(s,"title",this,t),a=tt(s,"afterTitle",this,t);let r=[];return r=ut(r,vt(n)),r=ut(r,vt(o)),r=ut(r,vt(a)),r}getBeforeBody(t,e){return kn(tt(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return I(t,o=>{const a={before:[],lines:[],after:[]},r=Sn(s,o);ut(a.before,vt(tt(r,"beforeLabel",this,o))),ut(a.lines,tt(r,"label",this,o)),ut(a.after,vt(tt(r,"afterLabel",this,o))),n.push(a)}),n}getAfterBody(t,e){return kn(tt(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=tt(s,"beforeFooter",this,t),o=tt(s,"footer",this,t),a=tt(s,"afterFooter",this,t);let r=[];return r=ut(r,vt(n)),r=ut(r,vt(o)),r=ut(r,vt(a)),r}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],a=[];let r=[],l,c;for(l=0,c=e.length;l<c;++l)r.push(wh(this.chart,e[l]));return t.filter&&(r=r.filter((h,d,f)=>t.filter(h,d,f,s))),t.itemSort&&(r=r.sort((h,d)=>t.itemSort(h,d,s))),I(r,h=>{const d=Sn(t.callbacks,h);n.push(tt(d,"labelColor",this,h)),o.push(tt(d,"labelPointStyle",this,h)),a.push(tt(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=a,this.dataPoints=r,r}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,a=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const r=ge[s.position].call(this,n,this._eventPosition);a=this._createItems(s),this.title=this.getTitle(a,s),this.beforeBody=this.getBeforeBody(a,s),this.body=this.getBody(a,s),this.afterBody=this.getAfterBody(a,s),this.footer=this.getFooter(a,s);const l=this._size=yn(this,s),c=Object.assign({},r,l),h=vn(this.chart,s,c),d=Mn(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:r.x,caretY:r.y}}this._tooltipItems=a,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:a,cornerRadius:r}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=Yt(r),{x:f,y:u}=t,{width:g,height:p}=e;let m,b,x,y,v,_;return o==="center"?(v=u+p/2,n==="left"?(m=f,b=m-a,y=v+a,_=v-a):(m=f+g,b=m+a,y=v-a,_=v+a),x=m):(n==="left"?b=f+Math.max(l,h)+a:n==="right"?b=f+g-Math.max(c,d)-a:b=this.caretX,o==="top"?(y=u,v=y-a,m=b-a,x=b+a):(y=u+p,v=y+a,m=b+a,x=b-a),_=y),{x1:m,x2:b,x3:x,y1:y,y2:v,y3:_}}drawTitle(t,e,s){const n=this.title,o=n.length;let a,r,l;if(o){const c=te(s.rtl,this.x,this.width);for(t.x=Ye(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",a=U(s.titleFont),r=s.titleSpacing,e.fillStyle=s.titleColor,e.font=a.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+a.lineHeight/2),t.y+=a.lineHeight+r,l+1===o&&(t.y+=s.titleMarginBottom-r)}}_drawColorBox(t,e,s,n,o){const a=this.labelColors[s],r=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=U(o.bodyFont),d=Ye(this,"left",o),f=n.x(d),u=l<h.lineHeight?(h.lineHeight-l)/2:0,g=e.y+u;if(o.usePointStyle){const p={radius:Math.min(c,l)/2,pointStyle:r.pointStyle,rotation:r.rotation,borderWidth:1},m=n.leftForLtr(f,c)+c/2,b=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,Ri(t,p,m,b),t.strokeStyle=a.borderColor,t.fillStyle=a.backgroundColor,Ri(t,p,m,b)}else{t.lineWidth=L(a.borderWidth)?Math.max(...Object.values(a.borderWidth)):a.borderWidth||1,t.strokeStyle=a.borderColor,t.setLineDash(a.borderDash||[]),t.lineDashOffset=a.borderDashOffset||0;const p=n.leftForLtr(f,c),m=n.leftForLtr(n.xPlus(f,1),c-2),b=Yt(a.borderRadius);Object.values(b).some(x=>x!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,we(t,{x:p,y:g,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=a.backgroundColor,t.beginPath(),we(t,{x:m,y:g+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(p,g,c,l),t.strokeRect(p,g,c,l),t.fillStyle=a.backgroundColor,t.fillRect(m,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:a,displayColors:r,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=U(s.bodyFont);let f=d.lineHeight,u=0;const g=te(s.rtl,this.x,this.width),p=function(w){e.fillText(w,g.x(t.x+u),t.y+f/2),t.y+=f+o},m=g.textAlign(a);let b,x,y,v,_,M,S;for(e.textAlign=a,e.textBaseline="middle",e.font=d.string,t.x=Ye(this,m,s),e.fillStyle=s.bodyColor,I(this.beforeBody,p),u=r&&m!=="right"?a==="center"?c/2+h:c+2+h:0,v=0,M=n.length;v<M;++v){for(b=n[v],x=this.labelTextColors[v],e.fillStyle=x,I(b.before,p),y=b.lines,r&&y.length&&(this._drawColorBox(e,t,v,g,s),f=Math.max(d.lineHeight,l)),_=0,S=y.length;_<S;++_)p(y[_]),f=d.lineHeight;I(b.after,p)}u=0,f=d.lineHeight,I(this.afterBody,p),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let a,r;if(o){const l=te(s.rtl,this.x,this.width);for(t.x=Ye(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",a=U(s.footerFont),e.fillStyle=s.footerColor,e.font=a.string,r=0;r<o;++r)e.fillText(n[r],l.x(t.x),t.y+a.lineHeight/2),t.y+=a.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:a}=this,{x:r,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:f,bottomLeft:u,bottomRight:g}=Yt(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(r+d,l),a==="top"&&this.drawCaret(t,e,s,n),e.lineTo(r+c-f,l),e.quadraticCurveTo(r+c,l,r+c,l+f),a==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(r+c,l+h-g),e.quadraticCurveTo(r+c,l+h,r+c-g,l+h),a==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(r+u,l+h),e.quadraticCurveTo(r,l+h,r,l+h-u),a==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(r,l+d),e.quadraticCurveTo(r,l,r+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const a=ge[t.position].call(this,this._active,this._eventPosition);if(!a)return;const r=this._size=yn(this,t),l=Object.assign({},a,this._size),c=vn(e,t,l),h=Mn(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=r.width,this.height=r.height,this.caretX=a.x,this.caretY=a.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const a=J(e.padding),r=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&r&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),ro(t,e.textDirection),o.y+=a.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),lo(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:r,index:l})=>{const c=this.chart.getDatasetMeta(r);if(!c)throw new Error("Cannot find a dataset at index "+r);return{datasetIndex:r,element:c.data[l],index:l}}),o=!ei(s,n),a=this._positionChanged(n,e);(o||a)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],a=this._getActiveElements(t,o,e,s),r=this._positionChanged(a,t),l=e||!ei(a,o)||r;return l&&(this._active=a,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(r=>this.chart.data.datasets[r.datasetIndex]&&this.chart.getDatasetMeta(r.datasetIndex).controller.getParsed(r.index)!==void 0);const a=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&a.reverse(),a}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,a=ge[o.position].call(this,t,e);return a!==!1&&(s!==a.x||n!==a.y)}}k(Wi,"positioners",ge);var Lh={id:"tooltip",_element:Wi,positioners:ge,afterInit(i,t,e){e&&(i.tooltip=new Wi({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Io},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},Rh=Object.freeze({__proto__:null,Colors:$c,Decimation:qc,Filler:ph,Legend:vh,SubTitle:Sh,Title:kh,Tooltip:Lh});const Eh=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function Ih(i,t,e,s){const n=i.indexOf(t);if(n===-1)return Eh(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const Fh=(i,t)=>i===null?null:K(Math.round(i),0,t);function wn(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class Ni extends Kt{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(O(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:Ih(s,t,D(e,t),this._addedLabels),Fh(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let a=t;a<=e;a++)n.push({value:a});return n}getLabelForValue(t){return wn.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}k(Ni,"id","category"),k(Ni,"defaults",{ticks:{callback:wn}});function zh(i,t){const e=[],{bounds:n,step:o,min:a,max:r,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:f}=i,u=o||1,g=h-1,{min:p,max:m}=t,b=!O(a),x=!O(r),y=!O(c),v=(m-p)/(d+1);let _=_s((m-p)/g/u)*u,M,S,w,P;if(_<1e-14&&!b&&!x)return[{value:p},{value:m}];P=Math.ceil(m/_)-Math.floor(p/_),P>g&&(_=_s(P*_/g/u)*u),O(l)||(M=Math.pow(10,l),_=Math.ceil(_*M)/M),n==="ticks"?(S=Math.floor(p/_)*_,w=Math.ceil(m/_)*_):(S=p,w=m),b&&x&&o&&Aa((r-a)/o,_/1e3)?(P=Math.round(Math.min((r-a)/_,h)),_=(r-a)/P,S=a,w=r):y?(S=b?a:S,w=x?r:w,P=c-1,_=(w-S)/P):(P=(w-S)/_,be(P,Math.round(P),_/1e3)?P=Math.round(P):P=Math.ceil(P));const C=Math.max(ys(_),ys(S));M=Math.pow(10,O(l)?C:l),S=Math.round(S*M)/M,w=Math.round(w*M)/M;let A=0;for(b&&(f&&S!==a?(e.push({value:a}),S<a&&A++,be(Math.round((S+A*_)*M)/M,a,Pn(a,v,i))&&A++):S<a&&A++);A<P;++A){const T=Math.round((S+A*_)*M)/M;if(x&&T>r)break;e.push({value:T})}return x&&f&&w!==r?e.length&&be(e[e.length-1].value,r,Pn(r,v,i))?e[e.length-1].value=r:e.push({value:r}):(!x||w===r)&&e.push({value:w}),e}function Pn(i,t,{horizontal:e,minRotation:s}){const n=lt(s),o=(e?Math.sin(n):Math.cos(n))||.001,a=.75*t*(""+i).length;return Math.min(t/o,a)}class li extends Kt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return O(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const a=l=>n=e?n:l,r=l=>o=s?o:l;if(t){const l=mt(n),c=mt(o);l<0&&c<0?r(0):l>0&&c>0&&a(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);r(o+l),t||a(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,a=zh(n,o);return t.bounds==="ticks"&&Yn(a,this,"value"),t.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return Oe(t,this.chart.options.locale,this.options.ticks.format)}}class Hi extends li{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=H(t)?t:0,this.max=H(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=lt(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}k(Hi,"id","linear"),k(Hi,"defaults",{ticks:{callback:ci.formatters.numeric}});const De=i=>Math.floor(At(i)),Wt=(i,t)=>Math.pow(10,De(i)+t);function Dn(i){return i/Math.pow(10,De(i))===1}function Cn(i,t,e){const s=Math.pow(10,e),n=Math.floor(i/s);return Math.ceil(t/s)-n}function Bh(i,t){const e=t-i;let s=De(e);for(;Cn(i,t,s)>10;)s++;for(;Cn(i,t,s)<10;)s--;return Math.min(s,De(i))}function Vh(i,{min:t,max:e}){t=ot(i.min,t);const s=[],n=De(t);let o=Bh(t,e),a=o<0?Math.pow(10,Math.abs(o)):1;const r=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((t-l)*a)/a,h=Math.floor((t-l)/r/10)*r*10;let d=Math.floor((c-h)/Math.pow(10,o)),f=ot(i.min,Math.round((l+h+d*Math.pow(10,o))*a)/a);for(;f<e;)s.push({value:f,major:Dn(f),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,a=o>=0?1:a),f=Math.round((l+h+d*Math.pow(10,o))*a)/a;const u=ot(i.max,f);return s.push({value:u,major:Dn(u),significand:d}),s}class ji extends Kt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const s=li.prototype.parse.apply(this,[t,e]);if(s===0){this._zero=!0;return}return H(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=H(t)?Math.max(0,t):null,this.max=H(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!H(this._userMin)&&(this.min=t===Wt(this.min,0)?Wt(this.min,-1):Wt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let s=this.min,n=this.max;const o=r=>s=t?s:r,a=r=>n=e?n:r;s===n&&(s<=0?(o(1),a(10)):(o(Wt(s,-1)),a(Wt(n,1)))),s<=0&&o(Wt(n,-1)),n<=0&&a(Wt(s,1)),this.min=s,this.max=n}buildTicks(){const t=this.options,e={min:this._userMin,max:this._userMax},s=Vh(e,this);return t.bounds==="ticks"&&Yn(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":Oe(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=At(t),this._valueRange=At(this.max)-At(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(At(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}k(ji,"id","logarithmic"),k(ji,"defaults",{ticks:{callback:ci.formatters.logarithmic,major:{enabled:!0}}});function Yi(i){const t=i.ticks;if(t.display&&i.display){const e=J(t.backdropPadding);return D(t.font&&t.font.size,N.font.size)+e.height}return 0}function Wh(i,t,e){return e=W(e)?e:[e],{w:Ua(i,t.string,e),h:e.length*t.lineHeight}}function An(i,t,e,s,n){return i===s||i===n?{start:t-e/2,end:t+e/2}:i<s||i>n?{start:t-e,end:t}:{start:t,end:t+e}}function Nh(i){const t={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},e=Object.assign({},t),s=[],n=[],o=i._pointLabels.length,a=i.options.pointLabels,r=a.centerPointLabels?R/o:0;for(let l=0;l<o;l++){const c=a.setContext(i.getPointLabelContext(l));n[l]=c.padding;const h=i.getPointPosition(l,i.drawingArea+n[l],r),d=U(c.font),f=Wh(i.ctx,d,i._pointLabels[l]);s[l]=f;const u=Z(i.getIndexAngle(l)+r),g=Math.round(Qi(u)),p=An(g,h.x,f.w,0,180),m=An(g,h.y,f.h,90,270);Hh(e,t,u,p,m)}i.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),i._pointLabelItems=$h(i,s,n)}function Hh(i,t,e,s,n){const o=Math.abs(Math.sin(e)),a=Math.abs(Math.cos(e));let r=0,l=0;s.start<t.l?(r=(t.l-s.start)/o,i.l=Math.min(i.l,t.l-r)):s.end>t.r&&(r=(s.end-t.r)/o,i.r=Math.max(i.r,t.r+r)),n.start<t.t?(l=(t.t-n.start)/a,i.t=Math.min(i.t,t.t-l)):n.end>t.b&&(l=(n.end-t.b)/a,i.b=Math.max(i.b,t.b+l))}function jh(i,t,e){const s=i.drawingArea,{extra:n,additionalAngle:o,padding:a,size:r}=e,l=i.getPointPosition(t,s+n+a,o),c=Math.round(Qi(Z(l.angle+Y))),h=Kh(l.y,r.h,c),d=Uh(c),f=Xh(l.x,r.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:f,top:h,right:f+r.w,bottom:h+r.h}}function Yh(i,t){if(!t)return!0;const{left:e,top:s,right:n,bottom:o}=i;return!(wt({x:e,y:s},t)||wt({x:e,y:o},t)||wt({x:n,y:s},t)||wt({x:n,y:o},t))}function $h(i,t,e){const s=[],n=i._pointLabels.length,o=i.options,{centerPointLabels:a,display:r}=o.pointLabels,l={extra:Yi(o)/2,additionalAngle:a?R/n:0};let c;for(let h=0;h<n;h++){l.padding=e[h],l.size=t[h];const d=jh(i,h,l);s.push(d),r==="auto"&&(d.visible=Yh(d,c),d.visible&&(c=d))}return s}function Uh(i){return i===0||i===180?"center":i<180?"left":"right"}function Xh(i,t,e){return e==="right"?i-=t:e==="center"&&(i-=t/2),i}function Kh(i,t,e){return e===90||e===270?i-=t/2:(e>270||e<90)&&(i-=t),i}function qh(i,t,e){const{left:s,top:n,right:o,bottom:a}=e,{backdropColor:r}=t;if(!O(r)){const l=Yt(t.borderRadius),c=J(t.backdropPadding);i.fillStyle=r;const h=s-c.left,d=n-c.top,f=o-s+c.width,u=a-n+c.height;Object.values(l).some(g=>g!==0)?(i.beginPath(),we(i,{x:h,y:d,w:f,h:u,radius:l}),i.fill()):i.fillRect(h,d,f,u)}}function Gh(i,t){const{ctx:e,options:{pointLabels:s}}=i;for(let n=t-1;n>=0;n--){const o=i._pointLabelItems[n];if(!o.visible)continue;const a=s.setContext(i.getPointLabelContext(n));qh(e,a,o);const r=U(a.font),{x:l,y:c,textAlign:h}=o;Xt(e,i._pointLabels[n],l,c+r.lineHeight/2,r,{color:a.color,textAlign:h,textBaseline:"middle"})}}function Fo(i,t,e,s){const{ctx:n}=i;if(e)n.arc(i.xCenter,i.yCenter,t,0,V);else{let o=i.getPointPosition(0,t);n.moveTo(o.x,o.y);for(let a=1;a<s;a++)o=i.getPointPosition(a,t),n.lineTo(o.x,o.y)}}function Zh(i,t,e,s,n){const o=i.ctx,a=t.circular,{color:r,lineWidth:l}=t;!a&&!s||!r||!l||e<0||(o.save(),o.strokeStyle=r,o.lineWidth=l,o.setLineDash(n.dash||[]),o.lineDashOffset=n.dashOffset,o.beginPath(),Fo(i,e,a,s),o.closePath(),o.stroke(),o.restore())}function Qh(i,t,e){return It(i,{label:e,index:t,type:"pointLabel"})}class pe extends li{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=J(Yi(this.options)/2),e=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(e,s)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=H(t)&&!isNaN(t)?t:0,this.max=H(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Yi(this.options))}generateTickLabels(t){li.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,s)=>{const n=z(this.options.pointLabels.callback,[e,s],this);return n||n===0?n:""}).filter((e,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?Nh(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,s,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,s,n))}getIndexAngle(t){const e=V/(this._pointLabels.length||1),s=this.options.startAngle||0;return Z(t*e+lt(s))}getDistanceFromCenterForValue(t){if(O(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(O(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const s=e[t];return Qh(this.getContext(),t,s)}}getPointPosition(t,e,s=0){const n=this.getIndexAngle(t)-Y+s;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:s,right:n,bottom:o}=this._pointLabelItems[t];return{left:e,top:s,right:n,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),Fo(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:s,grid:n,border:o}=e,a=this._pointLabels.length;let r,l,c;if(e.pointLabels.display&&Gh(this,a),n.display&&this.ticks.forEach((h,d)=>{if(d!==0||d===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const f=this.getContext(d),u=n.setContext(f),g=o.setContext(f);Zh(this,u,l,a,g)}}),s.display){for(t.save(),r=a-1;r>=0;r--){const h=s.setContext(this.getPointLabelContext(r)),{color:d,lineWidth:f}=h;!f||!d||(t.lineWidth=f,t.strokeStyle=d,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(e.reverse?this.min:this.max),c=this.getPointPosition(r,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,s=e.ticks;if(!s.display)return;const n=this.getIndexAngle(0);let o,a;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((r,l)=>{if(l===0&&this.min>=0&&!e.reverse)return;const c=s.setContext(this.getContext(l)),h=U(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,a=t.measureText(r.label).width,t.fillStyle=c.backdropColor;const d=J(c.backdropPadding);t.fillRect(-a/2-d.left,-o-h.size/2-d.top,a+d.width,h.size+d.height)}Xt(t,r.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}k(pe,"id","radialLinear"),k(pe,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:ci.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),k(pe,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),k(pe,"descriptors",{angleLines:{_fallback:"grid"}});const pi={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},et=Object.keys(pi);function On(i,t){return i-t}function Tn(i,t){if(O(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let a=t;return typeof s=="function"&&(a=s(a)),H(a)||(a=typeof s=="string"?e.parse(a,s):e.parse(a)),a===null?null:(n&&(a=n==="week"&&(ee(o)||o===!0)?e.startOf(a,"isoWeek",o):e.startOf(a,n)),+a)}function Ln(i,t,e,s){const n=et.length;for(let o=et.indexOf(i);o<n-1;++o){const a=pi[et[o]],r=a.steps?a.steps:Number.MAX_SAFE_INTEGER;if(a.common&&Math.ceil((e-t)/(r*a.size))<=s)return et[o]}return et[n-1]}function Jh(i,t,e,s,n){for(let o=et.length-1;o>=et.indexOf(e);o--){const a=et[o];if(pi[a].common&&i._adapter.diff(n,s,a)>=t-1)return a}return et[e?et.indexOf(e):0]}function td(i){for(let t=et.indexOf(i)+1,e=et.length;t<e;++t)if(pi[et[t]].common)return et[t]}function Rn(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=Ji(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function ed(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),a=t[t.length-1].value;let r,l;for(r=o;r<=a;r=+n.add(r,1,s))l=e[r],l>=0&&(t[l].major=!0);return t}function En(i,t,e){const s=[],n={},o=t.length;let a,r;for(a=0;a<o;++a)r=t[a],n[r]=a,s.push({value:r,major:!1});return o===0||!e?s:ed(i,s,n,e)}class Ce extends Kt{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new cl._date(t.adapters.date);n.init(e),me(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:Tn(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:a,maxDefined:r}=this.getUserBounds();function l(c){!a&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!r&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!a||!r)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=H(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=H(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,a=this.max,r=Ra(n,o,a);return this._unit=e.unit||(s.autoSkip?Ln(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Jh(this,r.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:td(this._unit),this.initOffsets(n),t.reverse&&r.reverse(),En(this,r,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const a=t.length<3?.5:.25;e=K(e,0,a),s=K(s,0,a),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,a=o.unit||Ln(o.minUnit,e,s,this._getLabelCapacity(e)),r=D(n.ticks.stepSize,1),l=a==="week"?o.isoWeekday:!1,c=ee(l)||l===!0,h={};let d=e,f,u;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":a),t.diff(s,e,a)>1e5*r)throw new Error(e+" and "+s+" are too far apart with stepSize of "+r+" "+a);const g=n.ticks.source==="data"&&this.getDataTimestamps();for(f=d,u=0;f<s;f=+t.add(f,r,a),u++)Rn(h,f,g);return(f===s||n.bounds==="ticks"||u===1)&&Rn(h,f,g),Object.keys(h).sort(On).map(p=>+p)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,a=e||n[o];return this._adapter.format(t,a)}_tickFormatFunction(t,e,s,n){const o=this.options,a=o.ticks.callback;if(a)return z(a,[t,e,s],this);const r=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&r[l],d=c&&r[c],f=s[e],u=c&&d&&f&&f.major;return this._adapter.format(t,n||(u?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=lt(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),a=Math.sin(n),r=this._resolveTickFontOptions(0).size;return{w:s*o+r*a,h:s*a+r*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,En(this,[t],this._majorUnit),n),a=this._getLabelSize(o),r=Math.floor(this.isHorizontal()?this.width/a.w:this.height/a.h)-1;return r>0?r:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(Tn(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Xn(t.sort(On))}}k(Ce,"id","time"),k(Ce,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function $e(i,t,e){let s=0,n=i.length-1,o,a,r,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=St(i,"pos",t)),{pos:o,time:r}=i[s],{pos:a,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=St(i,"time",t)),{time:o,pos:r}=i[s],{time:a,pos:l}=i[n]);const c=a-o;return c?r+(l-r)*(t-o)/c:r}class $i extends Ce{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=$e(e,this.min),this._tableRange=$e(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let a,r,l,c,h;for(a=0,r=t.length;a<r;++a)c=t[a],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(a=0,r=n.length;a<r;++a)h=n[a+1],l=n[a-1],c=n[a],Math.round((h+l)/2)!==c&&o.push({time:c,pos:a/(r-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return($e(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return $e(this._table,s*this._tableRange+this._minPos,!0)}}k($i,"id","timeseries"),k($i,"defaults",Ce.defaults);var id=Object.freeze({__proto__:null,CategoryScale:Ni,LinearScale:Hi,LogarithmicScale:ji,RadialLinearScale:pe,TimeScale:Ce,TimeSeriesScale:$i});const sd=[ll,Bc,Rh,id];pt.register(...sd);var nd=ne("<canvas></canvas>");function In(i,t){Ui(t,!0);let e,s;const n=zn(()=>{var r;const a=t.y1Label&&((r=t.data[0])==null?void 0:r.uniqueUsers)!==void 0;return{type:"bar",data:{labels:t.data.map(l=>l.hour!==void 0?`${l.hour}:00`:l.channelName),datasets:[{label:"Messages",data:t.data.map(l=>l.messageCount),backgroundColor:"rgba(41, 128, 185, 0.6)",borderColor:"rgba(41, 128, 185, 1)",borderWidth:1,yAxisID:"y"},...a?[{label:"Unique Users",data:t.data.map(l=>l.uniqueUsers),backgroundColor:"rgba(0, 204, 102, 0.6)",borderColor:"rgba(0, 204, 102, 1)",borderWidth:1,yAxisID:"y1"}]:[]]},options:{responsive:!0,maintainAspectRatio:!1,interaction:{mode:"index",intersect:!1},plugins:{title:{display:!0,text:t.title,color:"#f5f5f5"},legend:{position:"top",labels:{color:"#f5f5f5"}}},scales:{x:{title:{display:!0,text:t.xLabel,color:"#f5f5f5"},ticks:{color:"#f5f5f5"},grid:{color:"rgba(255, 255, 255, 0.1)"}},y:{type:"linear",display:!0,position:"left",title:{display:!0,text:t.yLabel,color:"#f5f5f5"},ticks:{color:"#f5f5f5"},grid:{color:"rgba(255, 255, 255, 0.1)"}},...a?{y1:{type:"linear",display:!0,position:"right",title:{display:!0,text:t.y1Label,color:"#f5f5f5"},ticks:{color:"#f5f5f5"},grid:{drawOnChartArea:!1}}}:{}}}}});Fn(()=>(s&&s.destroy(),s=new pt(e,rt(n)),()=>s==null?void 0:s.destroy()));var o=nd();Bn(o,a=>e=a,()=>e),Jt(i,o),Xi()}var od=ne("<canvas></canvas>");function ad(i,t){Ui(t,!0);let e,s;const n=zn(()=>({type:"doughnut",data:{labels:t.data.map(a=>a.message_type.charAt(0).toUpperCase()+a.message_type.slice(1)),datasets:[{data:t.data.map(a=>a.count),backgroundColor:["rgba(41, 128, 185, 0.8)","rgba(0, 204, 102, 0.8)","rgba(230, 126, 34, 0.8)","rgba(231, 76, 60, 0.8)","rgba(142, 68, 173, 0.8)","rgba(127, 140, 141, 0.8)"],borderColor:"#18181b",borderWidth:2}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:t.title,color:"#f5f5f5"},legend:{position:"bottom",labels:{color:"#f5f5f5"}},tooltip:{callbacks:{label:a=>{const r=t.data[a.dataIndex];return`${a.label}: ${a.formattedValue} (${r.percentage.toFixed(1)}%)`}}}}}}));Fn(()=>(s&&s.destroy(),s=new pt(e,rt(n)),()=>s==null?void 0:s.destroy()));var o=od();Bn(o,a=>e=a,()=>e),Jt(i,o),Xi()}var rd=ne("<p>Loading analytics...</p>"),ld=ne('<p class="error svelte-8jidpb"> </p>'),cd=ne('<div class="grid svelte-8jidpb"><div class="card svelte-8jidpb"><h3>Total Messages</h3> <p class="stat svelte-8jidpb"> </p></div> <div class="card svelte-8jidpb"><h3>Total Users</h3> <p class="stat svelte-8jidpb"> </p></div> <div class="card svelte-8jidpb"><h3>Total Channels</h3> <p class="stat svelte-8jidpb"> </p></div></div> <div class="grid chart-grid svelte-8jidpb"><div class="card svelte-8jidpb"><!></div> <div class="card svelte-8jidpb"><!></div> <div class="card svelte-8jidpb"><!></div></div>',1),hd=ne('<div class="container svelte-8jidpb"><h1>Analytics Dashboard</h1> <p>Showing data from the last 30 days.</p> <!></div>');function _d(i,t){Ui(t,!1);const[e,s]=Ho(),n=()=>jo(r,"$analyticsQuery",e),o=Xo(new Date),a=qo(o),r=Uo(a,o);Wo();var l=hd(),c=xt(st(l),4);{var h=f=>{var u=rd();Jt(f,u)},d=(f,u)=>{{var g=m=>{var b=ld(),x=st(b);nt(b),hs(()=>Te(x,`Error loading analytics: ${n().error.message??""}`)),Jt(m,b)},p=(m,b)=>{{var x=y=>{var v=cd();const _=ds(()=>n().data);var M=No(v),S=st(M),w=xt(st(S),2),P=st(w,!0);nt(w),nt(S);var C=xt(S,2),A=xt(st(C),2),T=st(A,!0);nt(A),nt(C);var $=xt(C,2),q=xt(st($),2),E=st(q,!0);nt(q),nt($),nt(M);var F=xt(M,2),B=st(F),it=st(B);{var X=j=>{In(j,{get data(){return rt(_).activityHeatmap},title:"Hourly Activity",xLabel:"Hour of Day",yLabel:"Messages",y1Label:"Unique Users"})};Gt(it,j=>{var ft;((ft=rt(_).activityHeatmap)==null?void 0:ft.length)>0&&j(X)})}nt(B);var dt=xt(B,2),qt=st(dt);{var Pt=j=>{ad(j,{get data(){return rt(_).messageDistribution},title:"Message Type Distribution"})};Gt(qt,j=>{var ft;((ft=rt(_).messageDistribution)==null?void 0:ft.length)>0&&j(Pt)})}nt(dt);var Dt=xt(dt,2),bt=st(Dt);{var Ft=j=>{In(j,{get data(){return rt(_).topChannels},title:"Top Channels",xLabel:"Channel",yLabel:"Messages"})};Gt(bt,j=>{var ft;((ft=rt(_).topChannels)==null?void 0:ft.length)>0&&j(Ft)})}nt(Dt),nt(F),hs((j,ft,zo)=>{Te(P,j),Te(T,ft),Te(E,zo)},[()=>rt(_).totalMessages.toLocaleString(),()=>rt(_).totalUsers.toLocaleString(),()=>rt(_).totalChannels.toLocaleString()],ds),Jt(y,v)};Gt(m,y=>{n().data&&y(x)},b)}};Gt(f,m=>{n().isError?m(g):m(p,!1)},u)}};Gt(c,f=>{n().isPending?f(h):f(d,!1)})}nt(l),Jt(i,l),Xi(),s()}export{_d as component};
