var Ht=e=>{throw TypeError(e)};var Ct=(e,t,s)=>t.has(e)||Ht("Cannot "+s);var n=(e,t,s)=>(Ct(e,t,"read from private field"),s?s.call(e):t.get(e)),m=(e,t,s)=>t.has(e)?Ht("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),l=(e,t,s,r)=>(Ct(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s),S=(e,t,s)=>(Ct(e,t,"access private method"),s);import{s as Fe,j as ie,aT as Dt,aU as Rt,aq as $t,aV as ae}from"./D8jla-3m.js";var Kt=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},nt=typeof window>"u"||"Deno"in globalThis;function gt(){}function ds(e,t){return typeof e=="function"?e(t):e}function It(e){return typeof e=="number"&&e>=0&&e!==1/0}function oe(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Y(e,t){return typeof e=="function"?e(t):e}function x(e,t){return typeof e=="function"?e(t):e}function fs(e,t){const{type:s="all",exact:r,fetchStatus:i,predicate:a,queryKey:f,stale:o}=e;if(f){if(r){if(t.queryHash!==Oe(f,t.options))return!1}else if(!_t(t.queryKey,f))return!1}if(s!=="all"){const c=t.isActive();if(s==="active"&&!c||s==="inactive"&&c)return!1}return!(typeof o=="boolean"&&t.isStale()!==o||i&&i!==t.state.fetchStatus||a&&!a(t))}function ys(e,t){const{exact:s,status:r,predicate:i,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(s){if(Ut(t.options.mutationKey)!==Ut(a))return!1}else if(!_t(t.options.mutationKey,a))return!1}return!(r&&t.state.status!==r||i&&!i(t))}function Oe(e,t){return((t==null?void 0:t.queryKeyHashFn)||Ut)(e)}function Ut(e){return JSON.stringify(e,(t,s)=>xt(s)?Object.keys(s).sort().reduce((r,i)=>(r[i]=s[i],r),{}):s)}function _t(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(s=>_t(e[s],t[s])):!1}function ue(e,t){if(e===t)return e;const s=Wt(e)&&Wt(t);if(s||xt(e)&&xt(t)){const r=s?e:Object.keys(e),i=r.length,a=s?t:Object.keys(t),f=a.length,o=s?[]:{},c=new Set(r);let d=0;for(let p=0;p<f;p++){const u=s?p:a[p];(!s&&c.has(u)||s)&&e[u]===void 0&&t[u]===void 0?(o[u]=void 0,d++):(o[u]=ue(e[u],t[u]),o[u]===e[u]&&e[u]!==void 0&&d++)}return i===f&&d===i?e:o}return t}function Tt(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}function Wt(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function xt(e){if(!Yt(e))return!1;const t=e.constructor;if(t===void 0)return!0;const s=t.prototype;return!(!Yt(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Yt(e){return Object.prototype.toString.call(e)==="[object Object]"}function De(e){return new Promise(t=>{setTimeout(t,e)})}function Nt(e,t,s){return typeof s.structuralSharing=="function"?s.structuralSharing(e,t):s.structuralSharing!==!1?ue(e,t):t}function ps(e,t,s=0){const r=[...e,t];return s&&r.length>s?r.slice(1):r}function ms(e,t,s=0){const r=[t,...e];return s&&r.length>s?r.slice(0,-1):r}var ce=Symbol();function Ie(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===ce?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var k,L,J,te,Ue=(te=class extends Kt{constructor(){super();m(this,k);m(this,L);m(this,J);l(this,J,t=>{if(!nt&&window.addEventListener){const s=()=>t();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){n(this,L)||this.setEventListener(n(this,J))}onUnsubscribe(){var t;this.hasListeners()||((t=n(this,L))==null||t.call(this),l(this,L,void 0))}setEventListener(t){var s;l(this,J,t),(s=n(this,L))==null||s.call(this),l(this,L,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){n(this,k)!==t&&(l(this,k,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(s=>{s(t)})}isFocused(){var t;return typeof n(this,k)=="boolean"?n(this,k):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},k=new WeakMap,L=new WeakMap,J=new WeakMap,te),he=new Ue,V,A,Z,ee,Te=(ee=class extends Kt{constructor(){super();m(this,V,!0);m(this,A);m(this,Z);l(this,Z,t=>{if(!nt&&window.addEventListener){const s=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",r)}}})}onSubscribe(){n(this,A)||this.setEventListener(n(this,Z))}onUnsubscribe(){var t;this.hasListeners()||((t=n(this,A))==null||t.call(this),l(this,A,void 0))}setEventListener(t){var s;l(this,Z,t),(s=n(this,A))==null||s.call(this),l(this,A,t(this.setOnline.bind(this)))}setOnline(t){n(this,V)!==t&&(l(this,V,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return n(this,V)}},V=new WeakMap,A=new WeakMap,Z=new WeakMap,ee),le=new Te;function Et(){let e,t;const s=new Promise((i,a)=>{e=i,t=a});s.status="pending",s.catch(()=>{});function r(i){Object.assign(s,i),delete s.resolve,delete s.reject}return s.resolve=i=>{r({status:"fulfilled",value:i}),e(i)},s.reject=i=>{r({status:"rejected",reason:i}),t(i)},s}function xe(e){return Math.min(1e3*2**e,3e4)}function de(e){return(e??"online")==="online"?le.isOnline():!0}var fe=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Ft(e){return e instanceof fe}function Ne(e){let t=!1,s=0,r=!1,i;const a=Et(),f=g=>{var w;r||(h(new fe(g)),(w=e.abort)==null||w.call(e))},o=()=>{t=!0},c=()=>{t=!1},d=()=>he.isFocused()&&(e.networkMode==="always"||le.isOnline())&&e.canRun(),p=()=>de(e.networkMode)&&e.canRun(),u=g=>{var w;r||(r=!0,(w=e.onSuccess)==null||w.call(e,g),i==null||i(),a.resolve(g))},h=g=>{var w;r||(r=!0,(w=e.onError)==null||w.call(e,g),i==null||i(),a.reject(g))},v=()=>new Promise(g=>{var w;i=O=>{(r||d())&&g(O)},(w=e.onPause)==null||w.call(e)}).then(()=>{var g;i=void 0,r||(g=e.onContinue)==null||g.call(e)}),R=()=>{if(r)return;let g;const w=s===0?e.initialPromise:void 0;try{g=w??e.fn()}catch(O){g=Promise.reject(O)}Promise.resolve(g).then(u).catch(O=>{var at;if(r)return;const E=e.retry??(nt?0:3),M=e.retryDelay??xe,it=typeof M=="function"?M(s,O):M,pt=E===!0||typeof E=="number"&&s<E||typeof E=="function"&&E(s,O);if(t||!pt){h(O);return}s++,(at=e.onFail)==null||at.call(e,s,O),De(it).then(()=>d()?void 0:v()).then(()=>{t?h(O):R()})})};return{promise:a,cancel:f,continue:()=>(i==null||i(),a),cancelRetry:o,continueRetry:c,canStart:p,start:()=>(p()?R():v().then(R),a)}}var Ee=e=>setTimeout(e,0);function Pe(){let e=[],t=0,s=o=>{o()},r=o=>{o()},i=Ee;const a=o=>{t?e.push(o):i(()=>{s(o)})},f=()=>{const o=e;e=[],o.length&&i(()=>{r(()=>{o.forEach(c=>{s(c)})})})};return{batch:o=>{let c;t++;try{c=o()}finally{t--,t||f()}return c},batchCalls:o=>(...c)=>{a(()=>{o(...c)})},schedule:a,setNotifyFunction:o=>{s=o},setBatchNotifyFunction:o=>{r=o},setScheduler:o=>{i=o}}}var zt=Pe(),$,se,Le=(se=class{constructor(){m(this,$)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),It(this.gcTime)&&l(this,$,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(nt?1/0:5*60*1e3))}clearGcTimeout(){n(this,$)&&(clearTimeout(n(this,$)),l(this,$,void 0))}},$=new WeakMap,se),X,K,U,_,C,ct,z,T,P,re,bs=(re=class extends Le{constructor(t){super();m(this,T);m(this,X);m(this,K);m(this,U);m(this,_);m(this,C);m(this,ct);m(this,z);l(this,z,!1),l(this,ct,t.defaultOptions),this.setOptions(t.options),this.observers=[],l(this,_,t.client),l(this,U,n(this,_).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,l(this,X,Ae(this.options)),this.state=t.state??n(this,X),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=n(this,C))==null?void 0:t.promise}setOptions(t){this.options={...n(this,ct),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&n(this,U).remove(this)}setData(t,s){const r=Nt(this.state.data,t,this.options);return S(this,T,P).call(this,{data:r,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),r}setState(t,s){S(this,T,P).call(this,{type:"setState",state:t,setStateOptions:s})}cancel(t){var r,i;const s=(r=n(this,C))==null?void 0:r.promise;return(i=n(this,C))==null||i.cancel(t),s?s.then(gt).catch(gt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(n(this,X))}isActive(){return this.observers.some(t=>x(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===ce||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>Y(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!oe(this.state.dataUpdatedAt,t)}onFocus(){var s;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(s=n(this,C))==null||s.continue()}onOnline(){var s;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(s=n(this,C))==null||s.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),n(this,U).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(s=>s!==t),this.observers.length||(n(this,C)&&(n(this,z)?n(this,C).cancel({revert:!0}):n(this,C).cancelRetry()),this.scheduleGc()),n(this,U).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||S(this,T,P).call(this,{type:"invalidate"})}fetch(t,s){var d,p,u;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(n(this,C))return n(this,C).continueRetry(),n(this,C).promise}if(t&&this.setOptions(t),!this.options.queryFn){const h=this.observers.find(v=>v.options.queryFn);h&&this.setOptions(h.options)}const r=new AbortController,i=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(l(this,z,!0),r.signal)})},a=()=>{const h=Ie(this.options,s),R=(()=>{const g={client:n(this,_),queryKey:this.queryKey,meta:this.meta};return i(g),g})();return l(this,z,!1),this.options.persister?this.options.persister(h,R,this):h(R)},o=(()=>{const h={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:n(this,_),state:this.state,fetchFn:a};return i(h),h})();(d=this.options.behavior)==null||d.onFetch(o,this),l(this,K,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((p=o.fetchOptions)==null?void 0:p.meta))&&S(this,T,P).call(this,{type:"fetch",meta:(u=o.fetchOptions)==null?void 0:u.meta});const c=h=>{var v,R,g,w;Ft(h)&&h.silent||S(this,T,P).call(this,{type:"error",error:h}),Ft(h)||((R=(v=n(this,U).config).onError)==null||R.call(v,h,this),(w=(g=n(this,U).config).onSettled)==null||w.call(g,this.state.data,h,this)),this.scheduleGc()};return l(this,C,Ne({initialPromise:s==null?void 0:s.initialPromise,fn:o.fetchFn,abort:r.abort.bind(r),onSuccess:h=>{var v,R,g,w;if(h===void 0){c(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(h)}catch(O){c(O);return}(R=(v=n(this,U).config).onSuccess)==null||R.call(v,h,this),(w=(g=n(this,U).config).onSettled)==null||w.call(g,h,this.state.error,this),this.scheduleGc()},onError:c,onFail:(h,v)=>{S(this,T,P).call(this,{type:"failed",failureCount:h,error:v})},onPause:()=>{S(this,T,P).call(this,{type:"pause"})},onContinue:()=>{S(this,T,P).call(this,{type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0})),n(this,C).start()}},X=new WeakMap,K=new WeakMap,U=new WeakMap,_=new WeakMap,C=new WeakMap,ct=new WeakMap,z=new WeakMap,T=new WeakSet,P=function(t){const s=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...ye(r.data,this.options),fetchMeta:t.meta??null};case"success":return l(this,K,void 0),{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const i=t.error;return Ft(i)&&i.revert&&n(this,K)?{...n(this,K),fetchStatus:"idle"}:{...r,error:i,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=s(this.state),zt.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),n(this,U).notify({query:this,type:"updated",action:t})})},re);function ye(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:de(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function Ae(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,s=t!==void 0,r=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var D,y,ht,F,G,tt,Q,j,lt,et,st,B,H,q,rt,b,ut,Pt,Lt,At,Qt,jt,qt,Mt,pe,ne,Qe=(ne=class extends Kt{constructor(t,s){super();m(this,b);m(this,D);m(this,y);m(this,ht);m(this,F);m(this,G);m(this,tt);m(this,Q);m(this,j);m(this,lt);m(this,et);m(this,st);m(this,B);m(this,H);m(this,q);m(this,rt,new Set);this.options=s,l(this,D,t),l(this,j,null),l(this,Q,Et()),this.options.experimental_prefetchInRender||n(this,Q).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(n(this,y).addObserver(this),Jt(n(this,y),this.options)?S(this,b,ut).call(this):this.updateResult(),S(this,b,Qt).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return kt(n(this,y),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return kt(n(this,y),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,S(this,b,jt).call(this),S(this,b,qt).call(this),n(this,y).removeObserver(this)}setOptions(t){const s=this.options,r=n(this,y);if(this.options=n(this,D).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof x(this.options.enabled,n(this,y))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");S(this,b,Mt).call(this),n(this,y).setOptions(this.options),s._defaulted&&!Tt(this.options,s)&&n(this,D).getQueryCache().notify({type:"observerOptionsUpdated",query:n(this,y),observer:this});const i=this.hasListeners();i&&Vt(n(this,y),r,this.options,s)&&S(this,b,ut).call(this),this.updateResult(),i&&(n(this,y)!==r||x(this.options.enabled,n(this,y))!==x(s.enabled,n(this,y))||Y(this.options.staleTime,n(this,y))!==Y(s.staleTime,n(this,y)))&&S(this,b,Pt).call(this);const a=S(this,b,Lt).call(this);i&&(n(this,y)!==r||x(this.options.enabled,n(this,y))!==x(s.enabled,n(this,y))||a!==n(this,q))&&S(this,b,At).call(this,a)}getOptimisticResult(t){const s=n(this,D).getQueryCache().build(n(this,D),t),r=this.createResult(s,t);return qe(this,r)&&(l(this,F,r),l(this,tt,this.options),l(this,G,n(this,y).state)),r}getCurrentResult(){return n(this,F)}trackResult(t,s){return new Proxy(t,{get:(r,i)=>(this.trackProp(i),s==null||s(i),Reflect.get(r,i))})}trackProp(t){n(this,rt).add(t)}getCurrentQuery(){return n(this,y)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=n(this,D).defaultQueryOptions(t),r=n(this,D).getQueryCache().build(n(this,D),s);return r.fetch().then(()=>this.createResult(r,s))}fetch(t){return S(this,b,ut).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),n(this,F)))}createResult(t,s){var Bt;const r=n(this,y),i=this.options,a=n(this,F),f=n(this,G),o=n(this,tt),d=t!==r?t.state:n(this,ht),{state:p}=t;let u={...p},h=!1,v;if(s._optimisticResults){const I=this.hasListeners(),mt=!I&&Jt(t,s),W=I&&Vt(t,r,s,i);(mt||W)&&(u={...u,...ye(p.data,t.options)}),s._optimisticResults==="isRestoring"&&(u.fetchStatus="idle")}let{error:R,errorUpdatedAt:g,status:w}=u;v=u.data;let O=!1;if(s.placeholderData!==void 0&&v===void 0&&w==="pending"){let I;a!=null&&a.isPlaceholderData&&s.placeholderData===(o==null?void 0:o.placeholderData)?(I=a.data,O=!0):I=typeof s.placeholderData=="function"?s.placeholderData((Bt=n(this,st))==null?void 0:Bt.state.data,n(this,st)):s.placeholderData,I!==void 0&&(w="success",v=Nt(a==null?void 0:a.data,I,s),h=!0)}if(s.select&&v!==void 0&&!O)if(a&&v===(f==null?void 0:f.data)&&s.select===n(this,lt))v=n(this,et);else try{l(this,lt,s.select),v=s.select(v),v=Nt(a==null?void 0:a.data,v,s),l(this,et,v),l(this,j,null)}catch(I){l(this,j,I)}n(this,j)&&(R=n(this,j),v=n(this,et),g=Date.now(),w="error");const E=u.fetchStatus==="fetching",M=w==="pending",it=w==="error",pt=M&&E,at=v!==void 0,N={status:w,fetchStatus:u.fetchStatus,isPending:M,isSuccess:w==="success",isError:it,isInitialLoading:pt,isLoading:pt,data:v,dataUpdatedAt:u.dataUpdatedAt,error:R,errorUpdatedAt:g,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>d.dataUpdateCount||u.errorUpdateCount>d.errorUpdateCount,isFetching:E,isRefetching:E&&!M,isLoadingError:it&&!at,isPaused:u.fetchStatus==="paused",isPlaceholderData:h,isRefetchError:it&&at,isStale:Gt(t,s),refetch:this.refetch,promise:n(this,Q)};if(this.options.experimental_prefetchInRender){const I=bt=>{N.status==="error"?bt.reject(N.error):N.data!==void 0&&bt.resolve(N.data)},mt=()=>{const bt=l(this,Q,N.promise=Et());I(bt)},W=n(this,Q);switch(W.status){case"pending":t.queryHash===r.queryHash&&I(W);break;case"fulfilled":(N.status==="error"||N.data!==W.value)&&mt();break;case"rejected":(N.status!=="error"||N.error!==W.reason)&&mt();break}}return N}updateResult(){const t=n(this,F),s=this.createResult(n(this,y),this.options);if(l(this,G,n(this,y).state),l(this,tt,this.options),n(this,G).data!==void 0&&l(this,st,n(this,y)),Tt(s,t))return;l(this,F,s);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:i}=this.options,a=typeof i=="function"?i():i;if(a==="all"||!a&&!n(this,rt).size)return!0;const f=new Set(a??n(this,rt));return this.options.throwOnError&&f.add("error"),Object.keys(n(this,F)).some(o=>{const c=o;return n(this,F)[c]!==t[c]&&f.has(c)})};S(this,b,pe).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&S(this,b,Qt).call(this)}},D=new WeakMap,y=new WeakMap,ht=new WeakMap,F=new WeakMap,G=new WeakMap,tt=new WeakMap,Q=new WeakMap,j=new WeakMap,lt=new WeakMap,et=new WeakMap,st=new WeakMap,B=new WeakMap,H=new WeakMap,q=new WeakMap,rt=new WeakMap,b=new WeakSet,ut=function(t){S(this,b,Mt).call(this);let s=n(this,y).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(gt)),s},Pt=function(){S(this,b,jt).call(this);const t=Y(this.options.staleTime,n(this,y));if(nt||n(this,F).isStale||!It(t))return;const r=oe(n(this,F).dataUpdatedAt,t)+1;l(this,B,setTimeout(()=>{n(this,F).isStale||this.updateResult()},r))},Lt=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(n(this,y)):this.options.refetchInterval)??!1},At=function(t){S(this,b,qt).call(this),l(this,q,t),!(nt||x(this.options.enabled,n(this,y))===!1||!It(n(this,q))||n(this,q)===0)&&l(this,H,setInterval(()=>{(this.options.refetchIntervalInBackground||he.isFocused())&&S(this,b,ut).call(this)},n(this,q)))},Qt=function(){S(this,b,Pt).call(this),S(this,b,At).call(this,S(this,b,Lt).call(this))},jt=function(){n(this,B)&&(clearTimeout(n(this,B)),l(this,B,void 0))},qt=function(){n(this,H)&&(clearInterval(n(this,H)),l(this,H,void 0))},Mt=function(){const t=n(this,D).getQueryCache().build(n(this,D),this.options);if(t===n(this,y))return;const s=n(this,y);l(this,y,t),l(this,ht,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},pe=function(t){zt.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(n(this,F))}),n(this,D).getQueryCache().notify({query:n(this,y),type:"observerResultsUpdated"})})},ne);function je(e,t){return x(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Jt(e,t){return je(e,t)||e.state.data!==void 0&&kt(e,t,t.refetchOnMount)}function kt(e,t,s){if(x(t.enabled,e)!==!1&&Y(t.staleTime,e)!=="static"){const r=typeof s=="function"?s(e):s;return r==="always"||r!==!1&&Gt(e,t)}return!1}function Vt(e,t,s,r){return(e!==t||x(r.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&Gt(e,s)}function Gt(e,t){return x(t.enabled,e)!==!1&&e.isStaleByTime(Y(t.staleTime,e))}function qe(e,t){return!Tt(e.getCurrentResult(),t)}const me="$$_queryClient",Me=()=>{const e=ie(me);if(!e)throw new Error("No QueryClient was found in Svelte context. Did you forget to wrap your component with QueryClientProvider?");return e},vs=e=>{Fe(me,e)},ke="$$_isRestoring",$e=()=>{try{const e=ie(ke);return e||Dt(!1)}catch{return Dt(!1)}};function Ke(){return $e()}function _e(e){return Me()}function ze(e){return"subscribe"in e&&typeof e.subscribe=="function"}function Ge(e,t,s){const r=_e(),i=Ke(),a=ze(e)?e:Dt(e),f=Rt([a,i],([p,u])=>{const h=r.defaultQueryOptions(p);return h._optimisticResults=u?"isRestoring":"optimistic",h}),o=new t(r,$t(f));f.subscribe(p=>{o.setOptions(p)});const c=Rt(i,(p,u)=>{const h=p?gt:o.subscribe(zt.batchCalls(u));return o.updateResult(),h}),{subscribe:d}=Rt([c,f],([p,u])=>(p=o.getOptimisticResult(u),u.notifyOnChangeProps?p:o.trackResult(p)));return{subscribe:d}}function dt(e,t){return Ge(e,Qe)}const gs=6048e5,Ss=864e5,be=6e4,ve=36e5,Zt=Symbol.for("constructDateFrom");function ge(e,t){return typeof e=="function"?e(t):e&&typeof e=="object"&&Zt in e?e[Zt](t):e instanceof Date?new e.constructor(t):new Date(t)}function Xt(e,t){return ge(t||e,e)}function Se(e,t){const s=()=>ge(t==null?void 0:t.in,NaN),i=Ye(e);let a;if(i.date){const d=Je(i.date,2);a=Ve(d.restDateString,d.year)}if(!a||isNaN(+a))return s();const f=+a;let o=0,c;if(i.time&&(o=Ze(i.time),isNaN(o)))return s();if(i.timezone){if(c=Xe(i.timezone),isNaN(c))return s()}else{const d=new Date(f+o),p=Xt(0,t==null?void 0:t.in);return p.setFullYear(d.getUTCFullYear(),d.getUTCMonth(),d.getUTCDate()),p.setHours(d.getUTCHours(),d.getUTCMinutes(),d.getUTCSeconds(),d.getUTCMilliseconds()),p}return Xt(f+o+c,t==null?void 0:t.in)}const vt={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Be=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,He=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,We=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Ye(e){const t={},s=e.split(vt.dateTimeDelimiter);let r;if(s.length>2)return t;if(/:/.test(s[0])?r=s[0]:(t.date=s[0],r=s[1],vt.timeZoneDelimiter.test(t.date)&&(t.date=e.split(vt.timeZoneDelimiter)[0],r=e.substr(t.date.length,e.length))),r){const i=vt.timezone.exec(r);i?(t.time=r.replace(i[1],""),t.timezone=i[1]):t.time=r}return t}function Je(e,t){const s=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),r=e.match(s);if(!r)return{year:NaN,restDateString:""};const i=r[1]?parseInt(r[1]):null,a=r[2]?parseInt(r[2]):null;return{year:a===null?i:a*100,restDateString:e.slice((r[1]||r[2]).length)}}function Ve(e,t){if(t===null)return new Date(NaN);const s=e.match(Be);if(!s)return new Date(NaN);const r=!!s[4],i=ot(s[1]),a=ot(s[2])-1,f=ot(s[3]),o=ot(s[4]),c=ot(s[5])-1;if(r)return ns(t,o,c)?ts(t,o,c):new Date(NaN);{const d=new Date(0);return!ss(t,a,f)||!rs(t,i)?new Date(NaN):(d.setUTCFullYear(t,a,Math.max(i,f)),d)}}function ot(e){return e?parseInt(e):1}function Ze(e){const t=e.match(He);if(!t)return NaN;const s=Ot(t[1]),r=Ot(t[2]),i=Ot(t[3]);return is(s,r,i)?s*ve+r*be+i*1e3:NaN}function Ot(e){return e&&parseFloat(e.replace(",","."))||0}function Xe(e){if(e==="Z")return 0;const t=e.match(We);if(!t)return 0;const s=t[1]==="+"?-1:1,r=parseInt(t[2]),i=t[3]&&parseInt(t[3])||0;return as(r,i)?s*(r*ve+i*be):NaN}function ts(e,t,s){const r=new Date(0);r.setUTCFullYear(e,0,4);const i=r.getUTCDay()||7,a=(t-1)*7+s+1-i;return r.setUTCDate(r.getUTCDate()+a),r}const es=[31,null,31,30,31,30,31,31,30,31,30,31];function we(e){return e%400===0||e%4===0&&e%100!==0}function ss(e,t,s){return t>=0&&t<=11&&s>=1&&s<=(es[t]||(we(e)?29:28))}function rs(e,t){return t>=1&&t<=(we(e)?366:365)}function ns(e,t,s){return t>=1&&t<=53&&s>=0&&s<=6}function is(e,t,s){return e===24?t===0&&s===0:s>=0&&s<60&&t>=0&&t<60&&e>=0&&e<25}function as(e,t){return t>=0&&t<=59}function Ce(e,t){const s=[];if(!t)return s;try{const r=t.split("/");for(const i of r){const[a,f]=i.split(":");if(!a||!f)continue;const o=f.split(",");for(const c of o){const[d,p]=c.split("-");if(!d||!p)continue;const u=Number(d),h=Number(p)+1;isNaN(u)||isNaN(h)||s.push({id:a,startIndex:u,endIndex:h,code:e.slice(u,h)})}}}catch(r){return console.error("Failed to parse emotes:",t,r),[]}return s}function os(){const e={showEmotes:{displayName:"Show Emotes",value:!0},showName:{displayName:"Show Name",value:!0},showTimestamp:{displayName:"Show Timestamp",value:!0},twitchChatMode:{displayName:"Twitch Chat Mode",value:!1},newOnBottom:{displayName:"Newest messages on bottom",value:!1}};let t=e;{const a=localStorage.getItem("rustlog:settings");if(a)try{t={...e,...JSON.parse(a)}}catch{}}const{subscribe:s,set:r,update:i}=ae(t);return{subscribe:s,set:a=>{localStorage.setItem("rustlog:settings",JSON.stringify(a)),r(a)},update:a=>{i(f=>{const o=a(f);return localStorage.setItem("rustlog:settings",JSON.stringify(o)),o})}}}const Re=os(),ws=ae(!1),us="";function St(e){return e.startsWith("id:")}function wt(e){return e.replace("id:","")}const ft={channels:["channels"],availableLogs:(e,t)=>["availableLogs",e,t],logs:(e,t,s,r)=>["logs",e,t,s,r],advancedSearch:(e,t,s)=>["advancedSearch",t,s,e],globalAnalytics:(e,t)=>["globalAnalytics",e,t]};async function yt(e,t){const s=await fetch(`${us}${e}`,t);if(!s.ok){const i=await s.text();throw new Error(i||`HTTP error! status: ${s.status}`)}const r=await s.text();return r?JSON.parse(r):null}function Cs(){return dt({queryKey:ft.channels,queryFn:async()=>{const e=await yt("/channels");return(e==null?void 0:e.channels)??[]}})}function Rs(e,t){return dt({queryKey:ft.availableLogs(e,t),queryFn:async()=>{if(!e)return[];let s="/list?";const r=new URLSearchParams;if(t){const f=St(t);r.append(`user${f?"id":""}`,f?wt(t):t)}const i=St(e);r.append(`channel${i?"id":""}`,i?wt(e):e),s+=r.toString();const a=await yt(s);return(a==null?void 0:a.availableLogs)??[]},enabled:!!e})}function Fs(e,t,s,r){const i=$t(Re);return dt({queryKey:ft.logs(e??"",t??"",s,r),queryFn:async()=>{if(!e||!t)return[];const a=St(e),f=St(t),o=a?wt(e):e,c=f?wt(t):t,d=`/channel${a?"id":""}/${o}/user${f?"id":""}/${c}/${s}/${r}`,p=new URLSearchParams({jsonBasic:"1"});i.newOnBottom||p.append("reverse","1");const u=await yt(`${d}?${p.toString()}`);return!u||!u.messages?[]:u.messages.map(h=>({...h,timestamp:Se(h.timestamp),emotes:Ce(h.text,h.tags.emotes)}))},enabled:!!e&&!!t})}function Os(e,t,s){const r=$t(Re),i=!!e.q||!!e.users&&e.users.length>0;return dt({queryKey:ft.advancedSearch(e,t,s),queryFn:async()=>{if(!i)return[];const a=new URLSearchParams({json:"1"});r.newOnBottom||a.append("reverse","1");for(const[c,d]of Object.entries(e))d&&(Array.isArray(d)?d.length>0&&a.append(c,d.join(",")):typeof d=="boolean"?a.append(c,"1"):d instanceof Date?a.append(c,d.toISOString()):a.append(c,String(d)));const o=await yt(`/advanced-search?${a.toString()}`);return!o||!o.messages?[]:o.messages.map(c=>({...c,timestamp:Se(c.timestamp),emotes:Ce(c.text,c.tags.emotes)}))},enabled:i})}function Ds(e,t){return dt({queryKey:ft.globalAnalytics(e,t),queryFn:async()=>{const s=new URLSearchParams({from:e.toISOString(),to:t.toISOString()});return yt(`/analytics?${s.toString()}`)}})}export{Ss as A,gs as B,bs as Q,Le as R,Kt as S,Fs as a,Os as b,ge as c,Ds as d,Ne as e,ys as f,gt as g,Oe as h,Ie as i,ms as j,ps as k,he as l,fs as m,zt as n,le as o,ds as p,Ut as q,Y as r,Re as s,Xt as t,Rs as u,_t as v,ce as w,vs as x,Cs as y,ws as z};
