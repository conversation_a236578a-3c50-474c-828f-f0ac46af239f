import{l as a,u as b,c as n,a as f,g as d,i as v,b as _,d as y,e as c,f as h,h as x,j as C,k,m as S,n as j,s as w,t as A,o as D}from"./D8jla-3m.js";import{c as E}from"./CSdymvmB.js";function M(){var t;return f===null&&d(),((t=f).ac??(t.ac=new AbortController)).signal}function p(t){n===null&&a(),y&&n.l!==null?u(n).m.push(t):b(()=>{const e=c(t);if(typeof e=="function")return e})}function O(t){n===null&&a(),p(()=>()=>c(t))}function P(t,e,{bubbles:s=!1,cancelable:l=!1}={}){return new CustomEvent(t,{detail:e,bubbles:s,cancelable:l})}function U(){const t=n;return t===null&&a(),(e,s,l)=>{var r;const o=(r=t.s.$$events)==null?void 0:r[e];if(o){const m=v(o)?o.slice():[o],i=P(e,s,l);for(const g of m)g.call(t.x,i);return!i.defaultPrevented}return!0}}function $(t){n===null&&a(),n.l===null&&_(),u(n).b.push(t)}function z(t){n===null&&a(),n.l===null&&_(),u(n).a.push(t)}function u(t){var e=t.l;return e.u??(e.u={a:[],b:[],m:[]})}const q=Object.freeze(Object.defineProperty({__proto__:null,afterUpdate:z,beforeUpdate:$,createEventDispatcher:U,createRawSnippet:E,flushSync:h,getAbortSignal:M,getAllContexts:x,getContext:C,hasContext:k,hydrate:S,mount:j,onDestroy:O,onMount:p,setContext:w,tick:A,unmount:D,untrack:c},Symbol.toStringTag,{value:"Module"}));export{O as a,p as o,q as s};
