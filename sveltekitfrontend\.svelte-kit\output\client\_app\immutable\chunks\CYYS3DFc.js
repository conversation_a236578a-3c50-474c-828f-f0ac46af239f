import"./CWj6FrbW.js";import{a0 as F,D as C,E as R,aw as dt,aW as ft,I as A,as as ht,aF as lt,aX as mt,aY as gt,G as I,ai as wt,F as yt,J as _,V as et,W as S,ad as G,ab as B,r as M,X as O,a2 as bt,$ as E,Y as nt,_ as X,a3 as $,a1 as V}from"./D8jla-3m.js";import{i as N,s as at,a as rt}from"./BFoZa56j.js";import{a as vt,e as Mt,i as Pt,s as j}from"./BPG35sX8.js";import{t as y,c as k,A as Ot,B as it,s as st}from"./CBqDoOLn.js";import{s as J}from"./Ce_2pv8Y.js";function kt(e,t,n=!1,a=!1,r=!1){var i=e,s="";F(()=>{var o=dt;if(s===(s=t()??"")){C&&R();return}if(o.nodes_start!==null&&(ft(o.nodes_start,o.nodes_end),o.nodes_start=o.nodes_end=null),s!==""){if(C){A.data;for(var u=R(),c=u;u!==null&&(u.nodeType!==ht||u.data!=="");)c=u,u=lt(u);if(u===null)throw mt(),gt;I(A,c),i=wt(u);return}var f=s+"";n?f=`<svg>${f}</svg>`:a&&(f=`<math>${f}</math>`);var m=yt(f);if((n||a)&&(m=_(m)),I(_(m),m.lastChild),n||a)for(;_(m);)i.before(_(m));else i.before(m)}})}function Q(e,t={},n,a){for(var r in n){var i=n[r];t[r]!==i&&(n[r]==null?e.style.removeProperty(r):e.style.setProperty(r,i,a))}}function xt(e,t,n,a){var r=e.__style;if(C||r!==t){var i=vt(t,a);(!C||i!==e.getAttribute("style"))&&(i==null?e.removeAttribute("style"):e.style.cssText=i),e.__style=t}else a&&(Array.isArray(a)?(Q(e,n==null?void 0:n[0],a[0]),Q(e,n==null?void 0:n[1],a[1],"important")):Q(e,n,a));return a}let Wt={};function q(){return Wt}function Y(e,t){var o,u,c,f;const n=q(),a=(t==null?void 0:t.weekStartsOn)??((u=(o=t==null?void 0:t.locale)==null?void 0:o.options)==null?void 0:u.weekStartsOn)??n.weekStartsOn??((f=(c=n.locale)==null?void 0:c.options)==null?void 0:f.weekStartsOn)??0,r=y(e,t==null?void 0:t.in),i=r.getDay(),s=(i<a?7:0)+i-a;return r.setDate(r.getDate()-s),r.setHours(0,0,0,0),r}function p(e,t){return Y(e,{...t,weekStartsOn:1})}function ot(e,t){const n=y(e,t==null?void 0:t.in),a=n.getFullYear(),r=k(n,0);r.setFullYear(a+1,0,4),r.setHours(0,0,0,0);const i=p(r),s=k(n,0);s.setFullYear(a,0,4),s.setHours(0,0,0,0);const o=p(s);return n.getTime()>=i.getTime()?a+1:n.getTime()>=o.getTime()?a:a-1}function U(e){const t=y(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function Dt(e,...t){const n=k.bind(null,t.find(a=>typeof a=="object"));return t.map(n)}function Yt(e,t,n){const[a,r]=Dt(n==null?void 0:n.in,e,t),i=J(a),s=J(r),o=+i-U(i),u=+s-U(s);return Math.round((o-u)/Ot)}function St(e,t){const n=ot(e,t),a=k(e,0);return a.setFullYear(n,0,4),a.setHours(0,0,0,0),p(a)}function Tt(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function _t(e){return!(!Tt(e)&&typeof e!="number"||isNaN(+y(e)))}function Et(e,t){const n=y(e,t==null?void 0:t.in);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}const Ft={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Ct=(e,t,n)=>{let a;const r=Ft[e];return typeof r=="string"?a=r:t===1?a=r.one:a=r.other.replace("{{count}}",t.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+a:a+" ago":a};function L(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const Nt={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},pt={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},qt={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Ht={date:L({formats:Nt,defaultWidth:"full"}),time:L({formats:pt,defaultWidth:"full"}),dateTime:L({formats:qt,defaultWidth:"full"})},Xt={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},jt=(e,t,n,a)=>Xt[e];function W(e){return(t,n)=>{const a=n!=null&&n.context?String(n.context):"standalone";let r;if(a==="formatting"&&e.formattingValues){const s=e.defaultFormattingWidth||e.defaultWidth,o=n!=null&&n.width?String(n.width):s;r=e.formattingValues[o]||e.formattingValues[s]}else{const s=e.defaultWidth,o=n!=null&&n.width?String(n.width):e.defaultWidth;r=e.values[o]||e.values[s]}const i=e.argumentCallback?e.argumentCallback(t):t;return r[i]}}const Qt={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Lt={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Rt={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},At={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},It={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Gt={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Bt=(e,t)=>{const n=Number(e),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},$t={ordinalNumber:Bt,era:W({values:Qt,defaultWidth:"wide"}),quarter:W({values:Lt,defaultWidth:"wide",argumentCallback:e=>e-1}),month:W({values:Rt,defaultWidth:"wide"}),day:W({values:At,defaultWidth:"wide"}),dayPeriod:W({values:It,defaultWidth:"wide",formattingValues:Gt,defaultFormattingWidth:"wide"})};function D(e){return(t,n={})=>{const a=n.width,r=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],i=t.match(r);if(!i)return null;const s=i[0],o=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(o)?Jt(o,m=>m.test(s)):Vt(o,m=>m.test(s));let c;c=e.valueCallback?e.valueCallback(u):u,c=n.valueCallback?n.valueCallback(c):c;const f=t.slice(s.length);return{value:c,rest:f}}}function Vt(e,t){for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}function Jt(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}function Ut(e){return(t,n={})=>{const a=t.match(e.matchPattern);if(!a)return null;const r=a[0],i=t.match(e.parsePattern);if(!i)return null;let s=e.valueCallback?e.valueCallback(i[0]):i[0];s=n.valueCallback?n.valueCallback(s):s;const o=t.slice(r.length);return{value:s,rest:o}}}const zt=/^(\d+)(th|st|nd|rd)?/i,Kt=/\d+/i,Zt={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},te={any:[/^b/i,/^(a|c)/i]},ee={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},ne={any:[/1/i,/2/i,/3/i,/4/i]},ae={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},re={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},ie={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},se={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},oe={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},ue={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},ce={ordinalNumber:Ut({matchPattern:zt,parsePattern:Kt,valueCallback:e=>parseInt(e,10)}),era:D({matchPatterns:Zt,defaultMatchWidth:"wide",parsePatterns:te,defaultParseWidth:"any"}),quarter:D({matchPatterns:ee,defaultMatchWidth:"wide",parsePatterns:ne,defaultParseWidth:"any",valueCallback:e=>e+1}),month:D({matchPatterns:ae,defaultMatchWidth:"wide",parsePatterns:re,defaultParseWidth:"any"}),day:D({matchPatterns:ie,defaultMatchWidth:"wide",parsePatterns:se,defaultParseWidth:"any"}),dayPeriod:D({matchPatterns:oe,defaultMatchWidth:"any",parsePatterns:ue,defaultParseWidth:"any"})},de={code:"en-US",formatDistance:Ct,formatLong:Ht,formatRelative:jt,localize:$t,match:ce,options:{weekStartsOn:0,firstWeekContainsDate:1}};function fe(e,t){const n=y(e,t==null?void 0:t.in);return Yt(n,Et(n))+1}function he(e,t){const n=y(e,t==null?void 0:t.in),a=+p(n)-+St(n);return Math.round(a/it)+1}function ut(e,t){var f,m,g,w;const n=y(e,t==null?void 0:t.in),a=n.getFullYear(),r=q(),i=(t==null?void 0:t.firstWeekContainsDate)??((m=(f=t==null?void 0:t.locale)==null?void 0:f.options)==null?void 0:m.firstWeekContainsDate)??r.firstWeekContainsDate??((w=(g=r.locale)==null?void 0:g.options)==null?void 0:w.firstWeekContainsDate)??1,s=k((t==null?void 0:t.in)||e,0);s.setFullYear(a+1,0,i),s.setHours(0,0,0,0);const o=Y(s,t),u=k((t==null?void 0:t.in)||e,0);u.setFullYear(a,0,i),u.setHours(0,0,0,0);const c=Y(u,t);return+n>=+o?a+1:+n>=+c?a:a-1}function le(e,t){var o,u,c,f;const n=q(),a=(t==null?void 0:t.firstWeekContainsDate)??((u=(o=t==null?void 0:t.locale)==null?void 0:o.options)==null?void 0:u.firstWeekContainsDate)??n.firstWeekContainsDate??((f=(c=n.locale)==null?void 0:c.options)==null?void 0:f.firstWeekContainsDate)??1,r=ut(e,t),i=k((t==null?void 0:t.in)||e,0);return i.setFullYear(r,0,a),i.setHours(0,0,0,0),Y(i,t)}function me(e,t){const n=y(e,t==null?void 0:t.in),a=+Y(n,t)-+le(n,t);return Math.round(a/it)+1}function h(e,t){const n=e<0?"-":"",a=Math.abs(e).toString().padStart(t,"0");return n+a}const v={y(e,t){const n=e.getFullYear(),a=n>0?n:1-n;return h(t==="yy"?a%100:a,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):h(n+1,2)},d(e,t){return h(e.getDate(),t.length)},a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(e,t){return h(e.getHours()%12||12,t.length)},H(e,t){return h(e.getHours(),t.length)},m(e,t){return h(e.getMinutes(),t.length)},s(e,t){return h(e.getSeconds(),t.length)},S(e,t){const n=t.length,a=e.getMilliseconds(),r=Math.trunc(a*Math.pow(10,n-3));return h(r,t.length)}},x={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},z={G:function(e,t,n){const a=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});case"GGGG":default:return n.era(a,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const a=e.getFullYear(),r=a>0?a:1-a;return n.ordinalNumber(r,{unit:"year"})}return v.y(e,t)},Y:function(e,t,n,a){const r=ut(e,a),i=r>0?r:1-r;if(t==="YY"){const s=i%100;return h(s,2)}return t==="Yo"?n.ordinalNumber(i,{unit:"year"}):h(i,t.length)},R:function(e,t){const n=ot(e);return h(n,t.length)},u:function(e,t){const n=e.getFullYear();return h(n,t.length)},Q:function(e,t,n){const a=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(a);case"QQ":return h(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(e,t,n){const a=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(a);case"qq":return h(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(e,t,n){const a=e.getMonth();switch(t){case"M":case"MM":return v.M(e,t);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(e,t,n){const a=e.getMonth();switch(t){case"L":return String(a+1);case"LL":return h(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(e,t,n,a){const r=me(e,a);return t==="wo"?n.ordinalNumber(r,{unit:"week"}):h(r,t.length)},I:function(e,t,n){const a=he(e);return t==="Io"?n.ordinalNumber(a,{unit:"week"}):h(a,t.length)},d:function(e,t,n){return t==="do"?n.ordinalNumber(e.getDate(),{unit:"date"}):v.d(e,t)},D:function(e,t,n){const a=fe(e);return t==="Do"?n.ordinalNumber(a,{unit:"dayOfYear"}):h(a,t.length)},E:function(e,t,n){const a=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});case"EEEE":default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(e,t,n,a){const r=e.getDay(),i=(r-a.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return h(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(r,{width:"short",context:"formatting"});case"eeee":default:return n.day(r,{width:"wide",context:"formatting"})}},c:function(e,t,n,a){const r=e.getDay(),i=(r-a.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return h(i,t.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(r,{width:"narrow",context:"standalone"});case"cccccc":return n.day(r,{width:"short",context:"standalone"});case"cccc":default:return n.day(r,{width:"wide",context:"standalone"})}},i:function(e,t,n){const a=e.getDay(),r=a===0?7:a;switch(t){case"i":return String(r);case"ii":return h(r,t.length);case"io":return n.ordinalNumber(r,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});case"iiii":default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(e,t,n){const r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){const a=e.getHours();let r;switch(a===12?r=x.noon:a===0?r=x.midnight:r=a/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,t,n){const a=e.getHours();let r;switch(a>=17?r=x.evening:a>=12?r=x.afternoon:a>=4?r=x.morning:r=x.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let a=e.getHours()%12;return a===0&&(a=12),n.ordinalNumber(a,{unit:"hour"})}return v.h(e,t)},H:function(e,t,n){return t==="Ho"?n.ordinalNumber(e.getHours(),{unit:"hour"}):v.H(e,t)},K:function(e,t,n){const a=e.getHours()%12;return t==="Ko"?n.ordinalNumber(a,{unit:"hour"}):h(a,t.length)},k:function(e,t,n){let a=e.getHours();return a===0&&(a=24),t==="ko"?n.ordinalNumber(a,{unit:"hour"}):h(a,t.length)},m:function(e,t,n){return t==="mo"?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):v.m(e,t)},s:function(e,t,n){return t==="so"?n.ordinalNumber(e.getSeconds(),{unit:"second"}):v.s(e,t)},S:function(e,t){return v.S(e,t)},X:function(e,t,n){const a=e.getTimezoneOffset();if(a===0)return"Z";switch(t){case"X":return Z(a);case"XXXX":case"XX":return P(a);case"XXXXX":case"XXX":default:return P(a,":")}},x:function(e,t,n){const a=e.getTimezoneOffset();switch(t){case"x":return Z(a);case"xxxx":case"xx":return P(a);case"xxxxx":case"xxx":default:return P(a,":")}},O:function(e,t,n){const a=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+K(a,":");case"OOOO":default:return"GMT"+P(a,":")}},z:function(e,t,n){const a=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+K(a,":");case"zzzz":default:return"GMT"+P(a,":")}},t:function(e,t,n){const a=Math.trunc(+e/1e3);return h(a,t.length)},T:function(e,t,n){return h(+e,t.length)}};function K(e,t=""){const n=e>0?"-":"+",a=Math.abs(e),r=Math.trunc(a/60),i=a%60;return i===0?n+String(r):n+String(r)+t+h(i,2)}function Z(e,t){return e%60===0?(e>0?"-":"+")+h(Math.abs(e)/60,2):P(e,t)}function P(e,t=""){const n=e>0?"-":"+",a=Math.abs(e),r=h(Math.trunc(a/60),2),i=h(a%60,2);return n+r+t+i}const tt=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},ct=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},ge=(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],a=n[1],r=n[2];if(!r)return tt(e,t);let i;switch(a){case"P":i=t.dateTime({width:"short"});break;case"PP":i=t.dateTime({width:"medium"});break;case"PPP":i=t.dateTime({width:"long"});break;case"PPPP":default:i=t.dateTime({width:"full"});break}return i.replace("{{date}}",tt(a,t)).replace("{{time}}",ct(r,t))},we={p:ct,P:ge},ye=/^D+$/,be=/^Y+$/,ve=["D","DD","YY","YYYY"];function Me(e){return ye.test(e)}function Pe(e){return be.test(e)}function Oe(e,t,n){const a=ke(e,t,n);if(console.warn(a),ve.includes(e))throw new RangeError(a)}function ke(e,t,n){const a=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${a} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const xe=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,We=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,De=/^'([^]*?)'?$/,Ye=/''/g,Se=/[a-zA-Z]/;function Te(e,t,n){var f,m,g,w;const a=q(),r=a.locale??de,i=a.firstWeekContainsDate??((m=(f=a.locale)==null?void 0:f.options)==null?void 0:m.firstWeekContainsDate)??1,s=a.weekStartsOn??((w=(g=a.locale)==null?void 0:g.options)==null?void 0:w.weekStartsOn)??0,o=y(e,n==null?void 0:n.in);if(!_t(o))throw new RangeError("Invalid time value");let u=t.match(We).map(d=>{const l=d[0];if(l==="p"||l==="P"){const b=we[l];return b(d,r.formatLong)}return d}).join("").match(xe).map(d=>{if(d==="''")return{isToken:!1,value:"'"};const l=d[0];if(l==="'")return{isToken:!1,value:_e(d)};if(z[l])return{isToken:!0,value:d};if(l.match(Se))throw new RangeError("Format string contains an unescaped latin alphabet character `"+l+"`");return{isToken:!1,value:d}});r.localize.preprocessor&&(u=r.localize.preprocessor(o,u));const c={firstWeekContainsDate:i,weekStartsOn:s,locale:r};return u.map(d=>{if(!d.isToken)return d.value;const l=d.value;(Pe(l)||Me(l))&&Oe(l,t,String(e));const b=z[l[0]];return b(o,l,r.localize,c)}).join("")}function _e(e){const t=e.match(De);return t?t[1].replace(Ye,"'"):e}var Ee=S('<img class="emote svelte-89mnao"/>'),Fe=S('<span class="message svelte-89mnao"></span>');function Ce(e,t){et(t,!0);const[n,a]=at(),r=()=>rt(st,"$settings",n);function i(u){var w;if(!r().showEmotes||!((w=u.emotes)!=null&&w.length))return[{type:"text",content:u.text}];const c=[];let f=0;const m=[...u.emotes].sort((d,l)=>d.startIndex-l.startIndex),g=u.text.split("");return m.forEach(d=>{d.startIndex>f&&c.push({type:"text",content:g.slice(f,d.startIndex).join("")}),c.push({type:"emote",content:d.code,id:d.id}),f=d.endIndex}),f<g.length&&c.push({type:"text",content:g.slice(f).join("")}),c}const s=bt(()=>i(t.message));var o=Fe();Mt(o,21,()=>M(s),Pt,(u,c)=>{var f=G(),m=B(f);{var g=d=>{var l=G(),b=B(l);kt(b,()=>M(c).content.replace(/(https?:\/\/[^\s]+)/g,'<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>')),O(d,l)},w=(d,l)=>{{var b=H=>{var T=Ee();F(()=>{j(T,"src",`https://static-cdn.jtvnw.net/emoticons/v2/${M(c).id}/default/dark/1.0`),j(T,"alt",M(c).content),j(T,"title",M(c).content)}),O(H,T)};N(d,H=>{M(c).type==="emote"&&H(b)},l)}};N(m,d=>{M(c).type==="text"?d(g):d(w,!1)})}O(u,f)}),E(o),O(e,o),nt(),a()}var Ne=S('<span class="timestamp svelte-3jkuhg"> </span>'),pe=S('<span class="user svelte-3jkuhg"> </span>'),qe=S('<div class="log-line svelte-3jkuhg"><!> <!> <!></div>');function Ae(e,t){et(t,!0);const[n,a]=at(),r=()=>rt(st,"$settings",n);function i(g){return g.color&&g.color.startsWith("#")?g.color:"#8a2be2"}var s=qe(),o=X(s);{var u=g=>{var w=Ne(),d=X(w,!0);E(w),F(l=>V(d,l),[()=>Te(t.message.timestamp,"yyyy-MM-dd HH:mm:ss")]),O(g,w)};N(o,g=>{r().showTimestamp&&g(u)})}var c=$(o,2);{var f=g=>{var w=pe();let d;var l=X(w);E(w),F(b=>{d=xt(w,"",d,b),V(l,`${t.message.displayName??""}:`)},[()=>({color:i(t.message.tags)})]),O(g,w)};N(c,g=>{r().showName&&g(f)})}var m=$(c,2);Ce(m,{get message(){return t.message}}),E(s),O(e,s),nt(),a()}export{Ae as L,xt as s};
