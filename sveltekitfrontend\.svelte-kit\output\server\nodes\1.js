

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.CiOc7Qnx.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/COxqGmGD.js","_app/immutable/chunks/D8jla-3m.js","_app/immutable/chunks/XmytlI-B.js","_app/immutable/chunks/50nLBstc.js","_app/immutable/chunks/CSdymvmB.js"];
export const stylesheets = [];
export const fonts = [];
