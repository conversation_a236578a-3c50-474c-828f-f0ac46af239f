var Yt=Object.defineProperty;var Ot=i=>{throw TypeError(i)};var Xt=(i,t,e)=>t in i?Yt(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var Rt=(i,t,e)=>Xt(i,typeof t!="symbol"?t+"":t,e),at=(i,t,e)=>t.has(i)||Ot("Cannot "+e);var o=(i,t,e)=>(at(i,t,"read from private field"),e?e.call(i):t.get(i)),m=(i,t,e)=>t.has(i)?Ot("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(i):t.set(i,e),ht=(i,t,e,s)=>(at(i,t,"write to private field"),s?s.call(i,e):t.set(i,e),e),X=(i,t,e)=>(at(i,t,"access private method"),e);import"./CWj6FrbW.js";import{i as $t,aM as te,aR as ee,R as ie,e as mt,Z as S,r as a,w as c,v as gt,V as se,u as lt,W as Dt,_ as re,a3 as Mt,$ as wt,a0 as Pt,X as L,Y as ne,ad as ft,ab as dt}from"./D8jla-3m.js";import{s as ut}from"./CSdymvmB.js";import{i as ct}from"./BFoZa56j.js";import{e as oe}from"./BPG35sX8.js";import{s as Lt}from"./CYYS3DFc.js";import{b as ae}from"./CzvPouDc.js";import{p as z}from"./9302ZZM5.js";const he=[];function b(i,t=!1){return tt(i,new Map,"",he)}function tt(i,t,e,s,n=null){if(typeof i=="object"&&i!==null){var h=t.get(i);if(h!==void 0)return h;if(i instanceof Map)return new Map(i);if(i instanceof Set)return new Set(i);if($t(i)){var l=Array(i.length);t.set(i,l),n!==null&&t.set(n,l);for(var f=0;f<i.length;f+=1){var u=i[f];f in i&&(l[f]=tt(u,t,e,s))}return l}if(te(i)===ee){l={},t.set(i,l),n!==null&&t.set(n,l);for(var g in i)l[g]=tt(i[g],t,e,s);return l}if(i instanceof Date)return structuredClone(i);if(typeof i.toJSON=="function")return tt(i.toJSON(),t,e,s,i)}if(i instanceof EventTarget)return i;try{return structuredClone(i)}catch{return i}}var E,w,_,et,kt;const it=class it{constructor(t){m(this,et);m(this,E,new WeakMap);m(this,w);m(this,_);ht(this,_,t)}observe(t,e){var s=o(this,E).get(t)||new Set;return s.add(e),o(this,E).set(t,s),X(this,et,kt).call(this).observe(t,o(this,_)),()=>{var n=o(this,E).get(t);n.delete(e),n.size===0&&(o(this,E).delete(t),o(this,w).unobserve(t))}}};E=new WeakMap,w=new WeakMap,_=new WeakMap,et=new WeakSet,kt=function(){return o(this,w)??ht(this,w,new ResizeObserver(t=>{for(var e of t){it.entries.set(e.target,e);for(var s of o(this,E).get(e.target)||[])s(e)}}))},Rt(it,"entries",new WeakMap);let St=it;var le=new St({box:"border-box"});function _t(i,t,e){var s=le.observe(i,()=>e(i[t]));ie(()=>(mt(()=>e(i[t])),s))}const $={START:"start",CENTER:"center",END:"end"},y={HORIZONTAL:"horizontal",VERTICAL:"vertical"},M={OBSERVED:0,REQUESTED:1},fe={[y.VERTICAL]:"top",[y.HORIZONTAL]:"left"},Nt={[y.VERTICAL]:"scrollTop",[y.HORIZONTAL]:"scrollLeft"};class de{constructor({itemSize:t,itemCount:e,estimatedItemSize:s}){this.itemSize=t,this.itemCount=e,this.estimatedItemSize=s,this.itemSizeAndPositionData={},this.lastMeasuredIndex=-1,this.checkForMismatchItemSizeAndItemCount(),this.justInTime||this.computeTotalSizeAndPositionData()}get justInTime(){return typeof this.itemSize=="function"}updateConfig({itemSize:t,itemCount:e,estimatedItemSize:s}){e!=null&&(this.itemCount=e),s!=null&&(this.estimatedItemSize=s),t!=null&&(this.itemSize=t),this.checkForMismatchItemSizeAndItemCount(),this.justInTime&&this.totalSize!=null?this.totalSize=void 0:this.computeTotalSizeAndPositionData()}checkForMismatchItemSizeAndItemCount(){if(Array.isArray(this.itemSize)&&this.itemSize.length<this.itemCount)throw Error("When itemSize is an array, itemSize.length can't be smaller than itemCount")}getSize(t){const{itemSize:e}=this;return typeof e=="function"?e(t):Array.isArray(e)?e[t]:e}computeTotalSizeAndPositionData(){let t=0;for(let e=0;e<this.itemCount;e++){const s=this.getSize(e),n=t;t+=s,this.itemSizeAndPositionData[e]={offset:n,size:s}}this.totalSize=t}getLastMeasuredIndex(){return this.lastMeasuredIndex}getSizeAndPositionForIndex(t){if(t<0||t>=this.itemCount)throw Error(`Requested index ${t} is outside of range 0..${this.itemCount}`);return this.justInTime?this.getJustInTimeSizeAndPositionForIndex(t):this.itemSizeAndPositionData[t]}getJustInTimeSizeAndPositionForIndex(t){if(t>this.lastMeasuredIndex){const e=this.getSizeAndPositionOfLastMeasuredItem();let s=e.offset+e.size;for(let n=this.lastMeasuredIndex+1;n<=t;n++){const h=this.getSize(n);if(h==null||Number.isNaN(h))throw Error(`Invalid size returned for index ${n} of value ${h}`);this.itemSizeAndPositionData[n]={offset:s,size:h},s+=h}this.lastMeasuredIndex=t}return this.itemSizeAndPositionData[t]}getSizeAndPositionOfLastMeasuredItem(){return this.lastMeasuredIndex>=0?this.itemSizeAndPositionData[this.lastMeasuredIndex]:{offset:0,size:0}}getTotalSize(){if(this.totalSize)return this.totalSize;const t=this.getSizeAndPositionOfLastMeasuredItem();return t.offset+t.size+(this.itemCount-this.lastMeasuredIndex-1)*this.estimatedItemSize}getUpdatedOffsetForIndex({align:t=$.START,containerSize:e,currentOffset:s,targetIndex:n}){if(e<=0)return 0;const h=this.getSizeAndPositionForIndex(n),l=h.offset,f=l-e+h.size;let u;switch(t){case $.END:u=f;break;case $.CENTER:u=l-(e-h.size)/2;break;case $.START:u=l;break;default:u=Math.max(f,Math.min(l,s))}const g=this.getTotalSize();return Math.max(0,Math.min(g-e,u))}getVisibleRange({containerSize:t=0,offset:e,overscanCount:s}){if(this.getTotalSize()===0)return{};const h=e+t;let l=this.findNearestItem(e);if(l===void 0)throw Error(`Invalid offset ${e} specified`);const f=this.getSizeAndPositionForIndex(l);e=f.offset+f.size;let u=l;for(;e<h&&u<this.itemCount-1;)u++,e+=this.getSizeAndPositionForIndex(u).size;return s&&(l=Math.max(0,l-s),u=Math.min(u+s,this.itemCount-1)),{start:l,stop:u}}resetItem(t){this.lastMeasuredIndex=Math.min(this.lastMeasuredIndex,t-1)}findNearestItem(t){if(Number.isNaN(t))throw Error(`Invalid offset ${t} specified`);t=Math.max(0,t);const e=this.getSizeAndPositionOfLastMeasuredItem(),s=Math.max(0,this.lastMeasuredIndex);return e.offset>=t?this.binarySearch({high:s,low:0,offset:t}):this.exponentialSearch({index:s,offset:t})}binarySearch({low:t,high:e,offset:s}){let n=0,h=0;for(;t<=e;){if(n=t+Math.floor((e-t)/2),h=this.getSizeAndPositionForIndex(n).offset,h===s)return n;h<s?t=n+1:h>s&&(e=n-1)}return t>0?t-1:0}exponentialSearch({index:t,offset:e}){let s=1;for(;t<this.itemCount&&this.getSizeAndPositionForIndex(t).offset<e;)t+=s,s*=2;return this.binarySearch({high:Math.min(t,this.itemCount-1),low:Math.floor(t/2),offset:e})}}var N,D,k,st,Ft;class ue{constructor(t=0){m(this,st);m(this,N,S(0));m(this,D,S(gt(M.REQUESTED)));m(this,k,S({offset:0,scrollChangeReason:M.REQUESTED}));this.offset=t}get offset(){return a(o(this,N))}set offset(t){c(o(this,N),t,!0)}get scrollChangeReason(){return a(o(this,D))}set scrollChangeReason(t){c(o(this,D),t,!0)}get previousState(){return a(o(this,k))}set previousState(t){c(o(this,k),t)}get doRefresh(){return this.offset!==this.previousState.offset||this.scrollChangeReason!==this.previousState.scrollChangeReason}get doScrollToOffset(){return this.offset!==this.previousState.offset&&this.scrollChangeReason===M.REQUESTED}listen(t,e){typeof t=="number"&&(this.offset=t),typeof e=="number"&&(this.scrollChangeReason=e)}update(){X(this,st,Ft).call(this)}}N=new WeakMap,D=new WeakMap,k=new WeakMap,st=new WeakSet,Ft=function(){this.previousState={offset:b(this.offset),scrollChangeReason:b(this.scrollChangeReason)}};var F,V,U,W,j,H,Q,B,J,Z,rt,Vt;class ce{constructor(t=-1,e="start",s=0,n=0,h=0,l=50,f=400,u=400,g=[]){m(this,rt);m(this,F,S(-1));m(this,V,S(""));m(this,U,S(0));m(this,W,S(0));m(this,j,S(0));m(this,H,S(0));m(this,Q,S(400));m(this,B,S(400));m(this,J,S(gt([])));m(this,Z,S({scrollToIndex:-1,scrollToAlignment:"start",scrollOffset:0,itemCount:0,itemSize:0,estimatedItemSize:50,height:400,width:400,stickyIndices:[]}));this.scrollToIndex=t,this.scrollToAlignment=e,this.scrollOffset=s,this.itemCount=n,this.itemSize=h,this.estimatedItemSize=l,this.height=f,this.width=u,this.stickyIndices=g}get scrollToIndex(){return a(o(this,F))}set scrollToIndex(t){c(o(this,F),t,!0)}get scrollToAlignment(){return a(o(this,V))}set scrollToAlignment(t){c(o(this,V),t,!0)}get scrollOffset(){return a(o(this,U))}set scrollOffset(t){c(o(this,U),t,!0)}get itemCount(){return a(o(this,W))}set itemCount(t){c(o(this,W),t,!0)}get itemSize(){return a(o(this,j))}set itemSize(t){c(o(this,j),t,!0)}get estimatedItemSize(){return a(o(this,H))}set estimatedItemSize(t){c(o(this,H),t,!0)}get height(){return a(o(this,Q))}set height(t){c(o(this,Q),t,!0)}get width(){return a(o(this,B))}set width(t){c(o(this,B),t,!0)}get stickyIndices(){return a(o(this,J))}set stickyIndices(t){c(o(this,J),t,!0)}get previousState(){return a(o(this,Z))}set previousState(t){c(o(this,Z),t)}get hasScrollOffsetChanged(){return this.previousState.scrollOffset!==this.scrollOffset}get haveScrollPropsChanged(){return this.previousState.scrollToIndex!==this.scrollToIndex||this.previousState.scrollToAlignment!==this.scrollToAlignment}get haveSizesChanged(){return this.previousState.itemCount!==this.itemCount||this.previousState.itemSize!==this.itemSize||this.previousState.estimatedItemSize!==this.estimatedItemSize}get hasScrollIndexChanged(){return this.scrollToIndex>-1&&(this.haveScrollPropsChanged||this.haveSizesChanged)}get haveDimsOrStickyIndicesChanged(){return this.previousState.height!==this.height||this.previousState.width!==this.width||this.previousState.stickyIndices.toString()!==b(this.stickyIndices).toString()}listen(t,e,s,n,h,l,f,u,g){typeof t=="number"&&(this.scrollToIndex=t),typeof e=="string"&&(this.scrollToAlignment=e),typeof s=="number"&&(this.scrollOffset=s),typeof n=="number"&&(this.itemCount=n),typeof n=="number"&&(this.itemCount=n),(typeof h=="number"||typeof h=="function"||Array.isArray(h))&&(this.itemSize=h),typeof l=="number"&&(this.estimatedItemSize=l||this.itemSize||50),typeof f=="number"&&(this.height=f),typeof u=="number"&&(this.width=u),Array.isArray(g)&&(this.stickyIndices=g)}update(){X(this,rt,Vt).call(this)}}F=new WeakMap,V=new WeakMap,U=new WeakMap,W=new WeakMap,j=new WeakMap,H=new WeakMap,Q=new WeakMap,B=new WeakMap,J=new WeakMap,Z=new WeakMap,rt=new WeakSet,Vt=function(){this.previousState={scrollToIndex:b(this.scrollToIndex),scrollToAlignment:b(this.scrollToAlignment),scrollOffset:b(this.scrollOffset),itemCount:b(this.itemCount),itemSize:b(this.itemSize),estimatedItemSize:b(this.estimatedItemSize)}};var me=Dt('<div class="virtual-list-inner svelte-1qhxbsx"></div>'),Se=Dt('<div class="virtual-list-wrapper svelte-1qhxbsx"><!> <!> <!></div>');function be(i,t){se(t,!0);let e=z(t,"height",3,400),s=z(t,"width",3,"100%"),n=z(t,"itemCount",3,0),h=z(t,"itemSize",3,0),l=z(t,"estimatedItemSize",3,0),f=z(t,"stickyIndices",19,()=>[]),u=z(t,"getKey",3,null),g=z(t,"scrollDirection",19,()=>y.VERTICAL),q=z(t,"scrollOffset",3,0),G=z(t,"scrollToIndex",19,()=>-1),K=z(t,"scrollToAlignment",3,"start"),Ut=z(t,"scrollToBehaviour",3,"instant"),Wt=z(t,"overscanCount",3,3),jt=z(t,"onListItemsUpdate",3,()=>{}),Ht=z(t,"onAfterScroll",3,()=>{}),x=S(void 0),P=S(gt({})),nt=S(""),ot=S(""),It=S(400),zt=S(400),pt=S([]);const p=new ce(G(),K(),q(),n(),h(),l(),Number.isFinite(e())?e():400,Number.isFinite(s())?s():400,f()),C=new ue(q()||0),R=new de({itemCount:n(),itemSize:h(),estimatedItemSize:p.estimatedItemSize});lt(()=>{const r={passive:!0};return a(x).addEventListener("scroll",Ct,r),()=>{a(x).removeEventListener("scroll",Ct,r)}}),lt(()=>{p.listen(G(),K(),q(),n(),h(),l(),Number.isFinite(e())?e():a(It),Number.isFinite(s())?s():a(zt),f()),mt(()=>{let r=!1;p.haveSizesChanged&&(R.updateConfig({itemSize:h(),itemCount:n(),estimatedItemSize:p.estimatedItemSize}),r=!0),p.hasScrollOffsetChanged?C.listen(p.scrollOffset,M.REQUESTED):p.hasScrollIndexChanged&&C.listen(Bt(G(),K()),M.REQUESTED),(p.haveDimsOrStickyIndicesChanged||r)&&Tt(),p.update()})}),lt(()=>{C.offset,mt(()=>{C.doRefresh&&At(),C.doScrollToOffset&&Qt(C.offset),C.update()})});const At=()=>{const{start:r,stop:d}=R.getVisibleRange({containerSize:g()===y.VERTICAL?p.height:p.width,offset:C.offset,overscanCount:Wt()}),I=[],T=R.getTotalSize(),A=typeof e()=="number"?"px":"",Y=typeof s()=="number"?"px":"";g()===y.VERTICAL?(c(nt,`height:${e()}${A};width:${s()}${Y};`),c(ot,`flex-direction:column;height:${T}px;`)):(c(nt,`height:${e()}${A};width:${s()}${Y};`),c(ot,`min-height:100%;width:${T}px;`));const yt=Array.isArray(f())&&f().length>0;if(yt)for(let v=0;v<f().length;v++){const Et=f()[v];I.push({index:Et,style:xt(Et,!0)})}if(r!==void 0&&d!==void 0){for(let v=r;v<=d;v++)yt&&f().includes(v)||I.push({index:v,style:xt(v,!1)});jt()({start:r,end:d})}c(pt,I)},Qt=r=>{"scroll"in a(x)?a(x).scroll({[fe[g()]]:r,behavior:Ut()}):a(x)[Nt[g()]]=r},Tt=(r=G())=>{c(P,{},!0),r>=0&&R.resetItem(r),At()},Bt=(r,d=K())=>((r<0||r>=n())&&(r=0),R.getUpdatedOffsetForIndex({align:d,containerSize:g()===y.VERTICAL?p.height:p.width,currentOffset:C.offset||0,targetIndex:r})),Ct=r=>{const d=Jt();if(d<0||C.offset===d||r.target!==a(x))return null;C.listen(d,M.OBSERVED),Ht()({offset:d,event:r})},Jt=()=>a(x)[Nt[g()]],xt=(r,d)=>{if(a(P)[r])return a(P)[r];const{size:I,offset:T}=R.getSizeAndPositionForIndex(r);let A;return g()===y.VERTICAL?(A=`left:0;width:100%;height:${I}px;`,d?A+=`position:sticky;flex-grow:0;z-index:1;top:0;margin-top:${T}px;margin-bottom:${-(T+I)}px;`:A+=`position:absolute;top:${T}px;`):(A=`top:0;width:${I}px;`,d?A+=`position:sticky;z-index:1;left:0;margin-left:${T}px;margin-right:${-(T+I)}px;`:A+=`position:absolute;height:100%;left:${T}px;`),a(P)[r]=A,a(P)[r]};var O=Se(),vt=re(O);{var Zt=r=>{var d=ft(),I=dt(d);ut(I,()=>t.header),L(r,d)};ct(vt,r=>{t.header&&r(Zt)})}var bt=Mt(vt,2);{var qt=r=>{var d=me();oe(d,21,()=>a(pt),I=>u()?u()(I.index):I.index,(I,T)=>{var A=ft(),Y=dt(A);ut(Y,()=>t.children,()=>({style:a(T).style,index:a(T).index})),L(I,A)}),wt(d),Pt(()=>Lt(d,a(ot))),L(r,d)};ct(bt,r=>{t.children&&r(qt)})}var Gt=Mt(bt,2);{var Kt=r=>{var d=ft(),I=dt(d);ut(I,()=>t.footer),L(r,d)};ct(Gt,r=>{t.footer&&r(Kt)})}return wt(O),ae(O,r=>c(x,r),()=>a(x)),Pt(()=>Lt(O,a(nt))),_t(O,"offsetHeight",r=>c(It,r)),_t(O,"offsetWidth",r=>c(zt,r)),L(i,O),ne({recomputeSizes:Tt})}export{be as V};
