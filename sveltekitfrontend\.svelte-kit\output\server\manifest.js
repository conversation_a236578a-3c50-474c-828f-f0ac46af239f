export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["favicon.svg"]),
	mimeTypes: {".svg":"image/svg+xml"},
	_: {
		client: {start:"_app/immutable/entry/start.B8L1lii4.js",app:"_app/immutable/entry/app.BbwGrOIY.js",imports:["_app/immutable/entry/start.B8L1lii4.js","_app/immutable/chunks/XmytlI-B.js","_app/immutable/chunks/50nLBstc.js","_app/immutable/chunks/D8jla-3m.js","_app/immutable/chunks/CSdymvmB.js","_app/immutable/entry/app.BbwGrOIY.js","_app/immutable/chunks/D8jla-3m.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/50nLBstc.js","_app/immutable/chunks/CSdymvmB.js","_app/immutable/chunks/BFoZa56j.js","_app/immutable/chunks/CzvPouDc.js","_app/immutable/chunks/9302ZZM5.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js')),
			__memo(() => import('./nodes/4.js')),
			__memo(() => import('./nodes/5.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 2 },
				endpoint: null
			},
			{
				id: "/analytics",
				pattern: /^\/analytics\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 3 },
				endpoint: null
			},
			{
				id: "/search",
				pattern: /^\/search\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 4 },
				endpoint: null
			},
			{
				id: "/tracker",
				pattern: /^\/tracker\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 5 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
