import{p as O,q as R,P as E,r as l,v as x,w as B,x as T,e as b,d as h,y as D,z as M,S as N,L as U,A as Y,B as q,C as w}from"./D8jla-3m.js";import{c as z}from"./BFoZa56j.js";function C(r){var n;return((n=r.ctx)==null?void 0:n.d)??!1}function G(r,n,u,s){var I;var f=!h||(u&D)!==0,v=(u&M)!==0,A=(u&w)!==0,a=s,c=!0,P=()=>(c&&(c=!1,a=A?b(s):s),a),_;if(v){var m=N in r||U in r;_=((I=O(r,n))==null?void 0:I.set)??(m&&n in r?e=>r[n]=e:void 0)}var d,o=!1;v?[d,o]=z(()=>r[n]):d=r[n],d===void 0&&s!==void 0&&(d=P(),_&&(f&&R(),_(d)));var i;if(f?i=()=>{var e=r[n];return e===void 0?P():(c=!0,e)}:i=()=>{var e=r[n];return e!==void 0&&(a=void 0),e===void 0?a:e},f&&(u&E)===0)return i;if(_){var L=r.$$legacy;return function(e,S){return arguments.length>0?((!f||!S||L||o)&&_(S?i():e),e):i()}}var t=((u&q)!==0?Y:T)(i);return v&&l(t),function(e,S){if(arguments.length>0){const g=S?l(t):f&&v?x(e):e;return B(t,g),a!==void 0&&(a=g),e}return C(t)?t.v:l(t)}}export{G as p};
