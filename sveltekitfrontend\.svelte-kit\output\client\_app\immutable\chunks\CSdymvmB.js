import{D as i,E as _,F as d,G as l,H as u,I as c,J as h,K as m,M as v,N as y,O as g,Q as E}from"./D8jla-3m.js";function F(t,s,...n){var a=t,e=g,r;m(()=>{e!==(e=s())&&(r&&(E(r),r=null),r=y(()=>e(a,...n)))},v),i&&(a=c)}function N(t){return(s,...n)=>{var o;var a=t(...n),e;if(i)e=c,_();else{var r=a.render().trim(),p=d(r);e=h(p),s.before(e)}const f=(o=a.setup)==null?void 0:o.call(a,e);l(e,e),typeof f=="function"&&u(f)}}export{N as c,F as s};
