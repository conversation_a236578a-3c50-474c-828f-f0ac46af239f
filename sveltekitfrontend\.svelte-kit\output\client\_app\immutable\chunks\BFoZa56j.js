import{K as D,D as _,E as h,M as O,ae as y,af as H,ag as U,ah as k,ai as q,aj as I,ak as T,N,al as E,am as w,I as F,H as L,an as M,O as A,ao as Y,ap as j,w as C,aq as K,r as P}from"./D8jla-3m.js";function $(e,n,[a,s]=[0,0]){_&&a===0&&h();var u=e,f=null,i=null,t=w,m=a>0?O:0,c=!1;const R=(o,r=!0)=>{c=!0,v(r,o)},v=(o,r)=>{if(t===(t=o))return;let g=!1;if(_&&s!==-1){if(a===0){const b=y(u);b===H?s=0:b===U?s=1/0:(s=parseInt(b.substring(1)),s!==s&&(s=t?1/0:-1))}const S=s>a;!!t===S&&(u=k(),q(u),I(!1),g=!0,s=-1)}t?(f?T(f):r&&(f=N(()=>r(u))),i&&E(i,()=>{i=null})):(i?T(i):r&&(i=N(()=>r(u,[a+1,s]))),f&&E(f,()=>{f=null})),g&&I(!0)};D(()=>{c=!1,n(R),c||v(null,null)},m),_&&(u=F)}let l=!1,p=Symbol();function d(e,n,a){const s=a[n]??(a[n]={store:null,source:Y(void 0),unsubscribe:A});if(s.store!==e&&!(p in a))if(s.unsubscribe(),s.store=e??null,e==null)s.source.v=void 0,s.unsubscribe=A;else{var u=!0;s.unsubscribe=j(e,f=>{u?s.source.v=f:C(s.source,f)}),u=!1}return e&&p in a?K(e):P(s.source)}function z(){const e={};function n(){L(()=>{for(var a in e)e[a].unsubscribe();M(e,p,{enumerable:!1,value:!0})})}return[e,n]}function B(e){var n=l;try{return l=!1,[e(),l]}finally{l=n}}export{d as a,B as c,$ as i,z as s};
